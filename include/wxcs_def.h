/****************************************************************************************
 * 文 件 名 : wxcs_def.h
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : zhengsw      '2019-01-03
* 编    码 : zhengsw      '2019-01-03
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#include "stddef.h"
#include "stdint.h"

#ifndef WXCS_DEF_H
#define WXCS_DEF_H
#define PACKED __attribute__((packed))

// 标签类型: config.ini --> TRAILER_TYPE 配置文件中的数值, 最终使用在这里
enum
{
    TRAILER_NO       =  0,  /* 没有 Trailer          */
    TRAILER_RT       =  1,  /* 戎腾设备 类型 TRAILER */
    TRAILER_HW       =  2,  /* 恒为设备 类型 TRAILER */
    TRAILER_JSC      =  3,  /* 金陵设备 类型 TRAILER */
    TRAILER_FL       =  4,  /* 赋乐设备 类型 TRAILER */
    TRAILER_HZ       =  5,  /* 汇智设备 类型 TRAILER */
    TRAILER_ZXSK     =  6,  /* 中新赛克 类型 TRAILER */
    TRAILER_HWZZ     =  7,  /* 恒为ZZ 类型 TRAILER */
};

struct hwzz_flag {
    uint8_t resv1 : 1;
    uint8_t resv2 : 1;
    uint8_t first_frame : 1;
    uint8_t direction : 1;
    uint8_t resv3 : 1;
    uint8_t resv4 : 1;
    uint8_t fin : 1;
    uint8_t resv5 : 1;
};

struct hwzz_data_source{
  uint8_t fromPosition : 4;
  uint8_t operator_ : 4 ;
}PACKED;

struct hwzz_eth_hdr {
    uint8_t                 queueNum;
    struct hwzz_data_source dataSource;
    uint32_t         imsi_hash;
    struct hwzz_flag flag;
    uint8_t          type;
    uint16_t                totLen;
    uint16_t                trailerLen;
} PACKED;

//此结构用于保存以太网头部部分信息
union eth_data{
	uint8_t   nsp;       //network service provider
	uint8_t   base;      //data source, 2G 3G 4G etc.
	uint16_t  vlan_id;   //for HW_SH_VLAN
	uint16_t  inter_id;  //for HY
	uint64_t  line_id;   //for 470
    struct{
        uint8_t  nsp;
        uint8_t  linktype;
        uint8_t  interface_num;
        uint8_t  channel_num;
        uint16_t line_num;
    }yunnan_470;
	uint64_t  data;      //compass above
  struct hwzz_eth_hdr  hwzz_eth ;
};

// 标准标签
typedef struct
{
    // 所有设备 trailer 字段的并集                      // *RT  *HW 说明
    char    TAGTYPE;                                    // [ ]  [*] 数据标签
    char    BFLAG;                                      // [ ]  [*] 运营商编号
    char    NCODE;                                      // [ ]  [*] 网络类型
    char    ACCOUNT[32];                                // [ ]  [*] 宽带账号
    char    ESN[32];                                    // [ ]  [*] 电子序列号
    char    MEID[32];                                   // [ ]  [*] 移动设备标识符
    short   LAC;                                        // [ ]  [*] 服务区编码
    short   SAC;                                        // [ ]  [*] 服务区编码
    short   CI;                                         // [ ]  [*] Cell_ID
    int     ECGI;                                       // [ ]  [*] E-UTRAN小区全局标识符
    short   ECGI_MNC;                                   // [ ]  [*] 位置信息的MNC
    char    BSID[32];                                   // [ ]  [*] 基站编码
    int     GRE_KEY;                                    // [ ]  [*] CDMA2000会话的key
    short   TAI;                                        // [ ]  [*] 4G_TAI
    char    APN[32];                                    // [ ]  [*] APN信息
    int     TEID;                                       // [*]  [*] GTP隧道标识
    int     OUTTER_SRC;                                 // [*]  [*] GTP外层SIP
    int     OUTTER_DST;                                 // [*]  [*] GTP外层DIP
    size_t  MSISDN;                                     // [*]  [*] 手机号
    size_t  IMEI;                                       // [*]  [*] 国际移动设备标识
    size_t  IMSI;                                       // [*]  [*] 国际移动用户识别码
    int     TAC;                                        // [*]  [*] 服务区编码
    short   PLMN_ID;                                    // [*]  [ ] 公共陆地移动网络,将MCC和MNC合并后转换成16进制数
    int     ULI;                                        // [*]  [ ] 用户位置信息(ECI)
    char    BS;                                         // [*]  [ ] 网络标识，3G/4G等
    char    DomainName[32];                             // [*]  [ ] DNS域名
    char    Operator  [32];                             // [ ]  [ ] 运营商名字
    char    DevName   [32];                             // [ ]  [ ] 解析板号

    /** 扩展使用 **/
    char    Area[32];                                   // 地域名
    int     DPI_Node_id;                                // DPI_节点_UUID (算法:Jhash salt=31)
    int     TS;                                         // 消息时间戳
    int     trailerType;                                // 标签类型
    char    init          ;
    uint8_t rat;                                        // 网络标识 和  BS 互斥
    char    rat_str[32]   ;                             // 网络类型字符串

    /* 中新赛克标签 */
    uint64_t nci;                                       // NR Cell Identity , 36 位, 标识 5G NR 小区
    char    fixed_account[64];                          // 固网账号
    char    direction;                                  // 数据流方向 1: c->s, 0: s->c, 2未知
    char    ruleID[32];                                  // 命中的规则id
    char    label[256];                                 // 标签，变长，应用和协议的标签
} PACKED ST_trailer;

/********* WXCS 消息类型 *************/
enum
{
    WXCS_MEDIA_CHAT = 100,  /* 微信话单        */
    WXCS_GROUP_HEAD,        /* 微信群图像      */
    WXCS_MOMENTS_VIDEO,     /* 朋友圈视频      */
    WXCS_POSITION,          /* 微信位置共享    */
    WXCS_QQ_SINGLE,         /* QQ 1对1 聊天    */
    WXCS_QQ_GROUP,          /* QQ 群聊         */
    WXCS_QQ_FILE,           /* QQ 文件传输     */
    WXCS_WX_GROUP_HEAD_DATA,/* 微信群头像数据   */
    WXCS_SKYPE_MEDIA_CHAT,  /* skype内部用户语音聊天 */
    WXCS_ZOOM_CHAT,         /* ZOOM 会议行为 */
    WXCS_WX_RELATION,       /* 微信ios 1对1 通联关系 */
    WXCS_WX_PEERS,          /* wx 一对一通话 公网ip */
    WXCS_QQ_EVENT,         /* ZOOM 会议行为 */
    WXCS_WX_LOC_SHARING,    /* wx 共享实时位置 */
    WXCS_TENCENT_MEETING,   /* 腾讯会议*/
    WXCS_ENUM_MAX,
};

/* [1] 微信话单消息体 */
#define CallTYpe_Calling     0
#define CallType_Called      1
#define CALLTYPE_UNSET       9
#define WXA_SESSION_PERSON   0
#define WXA_SESSION_GROUP    1
#define WXA_SESSION_NOT_SURE 7
#define VIDEO_PKT_SIZE       300 //经过试验得到: 语音包每包平均大小125~164， 视频平均每包330~380
typedef struct ST_wxAudioSessionAlive
{
    // 建联信息, 必须置于消息体的头部
    ST_trailer trailer;

    /* 会话信息 */
    int       Session_hash;                      // Session_hash
    uint8_t   SessionID[8];                      // 8字节，raw data
    uint8_t   SessionType;                       // 0为"个人"，1为"群组"
    uint8_t   SessionCalling;                    // 1为主叫, 0为被叫
    int       ip_version;                        // IP版本
    union     {int ipv4;char ipv6[16];}client_ip;// IP报文 公网IP
    union     {int ipv4;char ipv6[16];}server_ip;// IP报文 公网IP
    uint16_t  client_port;                       // IP报文 公网 Port
    uint16_t  server_port;                       // IP报文 公网 Port
    uint8_t   isTimeout;                         // 这个人是否已经超时

    /* 附加信息 */
    uint32_t  PersonC2STransPackets;             // 用户传输到服务器报文总数
    uint32_t  PersonS2CTransPackets;             // 服务器传输到用户报文总数
    uint32_t  PersonC2SVideoPackets;             // 用户传输到服务器视频报文数量
    uint32_t  PersonS2CVideoPackets;             // 服务器传输到用户视频报文数量
    uint32_t  PersonS2CUnknown;                  // 流中包含的未识别的包数
    uint32_t  PersonC2SUnknown;                  // 流中包含的未识别的包数
    uint32_t  PersonS2C_D5_Pcaket;               // 流中包含的前缀为D5包数
    uint32_t  PersonC2S_D5_Pcaket;               // 流中包含的前缀为D5包数
    uint32_t  PersonC2S_pkt_tcp;                 // TCP 报文 块数
    uint32_t  PersonS2C_pkt_tcp;                 // TCP 报文 块数
    uint32_t  Person_pkt_75;                     // 前缀为75 报文数
    uint32_t  Person_pkt_76;                     // 前缀为76 报文数
    uint32_t  Person_pkt_77;                     // 前缀为77 报文数
    uint32_t  Person_pkt_95;                     // 前缀为95 报文数
    uint32_t  Person_pkt_96;                     // 前缀为96 报文数
    uint32_t  Person_pkt_97;                     // 前缀为97 报文数
    uint32_t  Person_pkt_98;                     // 前缀为98 报文数
    uint32_t  Person_pkt_drop;                   // 不合规   报文数
    uint32_t  PersonC2STransBytes;               // 用户传输到服务器字节总数
    uint32_t  PersonS2CTransBytes;               // 服务器传输到用户字节总数
    uint32_t  PersonFirstActiveTime;             // 用户加入语音会话的时间
    uint32_t  PersonLastActiveTime;              // 用户的上次活跃时间
    uint32_t  PersonIsAnswered;                  // 是否被接听
    char      callflag[32];                     // 主被叫标记
    char      flowflag[32];                     // 流标记
    uint32_t  PersonRingTime;                    // 响铃时长

    uint32_t   stat_callflag_96_0;               // 96 c2s 主叫统计 0
    uint32_t   stat_callflag_96_1;               // 96 c2s 被叫统计 1
    uint32_t   stat_callflag_98_0;               // 98 c2s 主叫统计 0
    uint32_t   stat_callflag_98_1;               // 98 c2s 被叫统计 1

    // 数据包中自主计数信息
    uint32_t    c2s_pkts;
    uint32_t    s2c_pkts;
    uint32_t    c2s_voice_pkts;
    uint32_t    s2c_voice_pkts;
    uint32_t    c2s_video_pkts;
    uint32_t    s2c_video_pkts;

    // 话单 flow  session 用部分
    uint8_t send_flag;      // 是否发送给聚合
    int RingTimeStart;
    int RingTimeLast;
    int SequenceC2S;
    int SequenceS2C;
    uint32_t lasttime;

} ST_wxAudioSessionAlive;

/* [2] 微信群图像消息体*/
typedef struct
{
    // 建联信息, 必须置于消息体的头部
    ST_trailer trailer;

    uint16_t  PersonSrcPort;
    uint16_t  PersonDstPort;
    uint32_t  PersonSrcIp;
    uint32_t  PersonDstIp;
    int       ip_version;                        // IP版本
    union     {int ipv4;char ipv6[16];}client_ip;// IP报文 公网IP
    union     {int ipv4;char ipv6[16];}server_ip;// IP报文 公网IP

    /* wx group */
    char      PersonHost          [32];
    char      PersonURL           [1024];
    char      PersonUin           [32];
    char      PersonRef           [1024];
    char      PersonAgent         [256];
    char      PersonAcceptEncoding[16];
    char      PersonAcceptLanguage[16];
    uint32_t  PersonLastActiveTime;

    char      GroupHeadPictureName[256];
} ST_wxGroupHead;

/* 微信群头像数据 */
typedef struct {
    char      SessionUrl[1024];
    char      GroupHeadPictureName[256];
    uint32_t  PictureDataLen;
    uint8_t   PictureData[0];
} ST_wxGroupHeadPicData;

/* [3] 朋友圈视频消息体 */
typedef struct
{
    // 建联信息, 必须置于消息体的头部
    ST_trailer trailer;

    /* IP相关 */
    uint16_t   PersonSrcPort;
    uint16_t   PersonDstPort;
    uint32_t   PersonSrcIp;
    uint32_t   PersonDstIp;

    /****** 公共部分 ************/
    char      fileurl       [1024];
    char      thumburl      [1024];
    char      filekey       [32];
    char      clientversion [32];
    char      weixinnum     [32];
    char      clientostype  [32];
    char      nettype       [32];
    char      authkey       [32];
    uint64_t  ActiveTime;

    /****** 上传视频 ************/
    int       isUploader;
    char      filetype      [32];
    char      totalsize     [32];
    char      localname     [32];
    char      fileid        [32];

    /***** 下载观看视频 **********/
    int       isDownloader;
    int       isplayed;

} ST_weixin_moments_Video;

/* [4] 微信位置消息体 */
typedef struct
{
    // 建联信息, 必须置于消息体的头部
    ST_trailer trailer;

    uint16_t PersonSrcPort;
    uint16_t PersonDstPort;
    uint32_t PersonSrcIp;
    uint32_t PersonDstIp;

    /* wx position */
    char     PersonHost[32];
    char     PersonURL[1024];
    char     PersonAgent[256];
    char     PersonAcceptEncoding[16];
    uint32_t PersonLastActiveTime;
}ST_wxPosition;


/*! [5.1] 会话类型 所有会话类型都在这里添加 */
enum
{
    WXCS_SESSION_NONE = 0,            /**< 未知会话类型，用作默认值 */

    /* 话单会话类型 */
    WXCS_SESSION_CHAT_SINGLE_AUDIO =  0, /* 1对1 语音通话 */
    WXCS_SESSION_CHAT_GROUP_AUDIO  =  1, /* 群   语音通话 */
    WXCS_SESSION_CHAT_SINGLE_VIDEO = 10, /* 1对1 视频通话 */
    WXCS_SESSION_CHAT_GROUP_VIDEO  = 11, /* 群   视频通话 */
    WXCS_SESSION_CHAT_UNKNOWN      = -1, /* 未知     通话 */

    /* 传文件会话类型 */
    WXCS_SESSION_FILE_SINGLE = 5,       /** 1对1传文件    */
    WXCS_SESSION_FILE_GROUP = 6,        /** 群传文件      */
};

#define QQ_GROUP_MAX_MEMBER_COUNT 12   /*! 群通话成员最大数量 */
#define QQ_GROUP_MAX_DISSECT_MEMBER_COUNT 1000   /*! 群通话成员最大数量 */
typedef struct {
    // 建联信息, 必须置于消息体的头部
    ST_trailer trailer;

    uint32_t srcIp;
    uint32_t dstIp;
    uint16_t srcPort;
    uint16_t dstPort;

    uint32_t pubSrcIP;

    uint32_t c2sPackCount;
    uint32_t c2sByteCount;
    uint32_t s2cPackCount;
    uint32_t s2cByteCount;

    uint32_t groupId;
    uint64_t selfQQNum;
    uint64_t otherQQNums[QQ_GROUP_MAX_DISSECT_MEMBER_COUNT];
    uint64_t qqSession[2];

    uint32_t firstActiveTime;
    uint32_t lastActiveTime;
    uint8_t  isTimeout;                         // 这个人是否已经超时
} ST_qqGroup;

/*! [5.2] QQ 1对1通话消息格式 */
typedef struct {
    // 建联信息, 必须置于消息体的头部
    ST_trailer trailer;

    //客户端私网
    uint32_t srcIp;
    uint32_t dstIp;
    uint16_t srcPort;
    uint16_t dstPort;

    uint32_t pubSrcIP;

    uint32_t c2sPackCount;
    uint32_t c2sByteCount;
    uint32_t s2cPackCount;
    uint32_t s2cByteCount;

    uint64_t selfQQNum;
    uint64_t otherQQNum;

    uint64_t qqSession[2];

    uint32_t firstActiveTime;
    uint32_t lastActiveTime;
    uint8_t  isTimeout;                         // 这个人是否已经超时

    uint8_t  sessionType;                      //音視頻
} ST_qqSingle;

/* [6] QQ 传输文件 */
#define QQ_FILE_TYPE_P2P   0
#define QQ_FILE_TYPE_GROUP 1
typedef struct
{
    // 建联信息, 必须置于消息体的头部
    ST_trailer trailer;

    int       ip_version;
    union     {int ipv4;char ipv6[16];}client_ip;
    union     {int ipv4;char ipv6[16];}server_ip;
    uint16_t  client_port;
    uint16_t  server_port;

    char      isGroup;               // 群行为?
    char      isSender;              // 1:文件发送者, 0:文件为接受者
    char      FileEncode    [128];   // 被编码后的文件名
    size_t    QQNum;                 // QQ号码
    char      Method        [8];
    char      Host          [1024];
    char      URL           [4096];
    char      User_Agent    [256];
    char      AcceptEncoding[32];
    char      AcceptLanguage[32];
    char      Connection    [16];
    char      ContentLength [16];
    char      Cookie        [64];
    char      CacheControl  [32];
    char      NetType       [16];
    char      isPicture;
    char      picture_width [6];
    char      picture_height[6];
    int       LastActiveTime;
} PACKED ST_QQ_File_MSG;

/* [7] skype 语音聊天消息 */
typedef struct ST_SkypeMediaSessionAlive
{
    // 建联信息, 必须置于消息体的头部
    ST_trailer trailer;

    /* 会话信息 */
    uint32_t  Session_hash;                       // Session_hash
    uint8_t   SessionID[10];                      // 9字节有效数据
    uint8_t   SessionType;                        // 尚未确定
    uint8_t   SessionCalling;                     // 尚未确定
    uint16_t  ip_version;                         // IP版本
    union     {int ipv4;char ipv6[16];}client_ip; // IP报文 公网IP
    union     {int ipv4;char ipv6[16];}server_ip; // IP报文 公网IP
    uint16_t  client_port;                       // IP报文 公网 Port
    uint16_t  server_port;                       // IP报文 公网 Port
    uint8_t   isTimeout;                          // 这个人是否已经超时

    /* 附加信息 */
    uint32_t  PersonA2BTransPackets;              // 用户A发给用户B报文总数
    uint32_t  PersonB2ATransPackets;              // 用户B发给用户A报文总数
    uint32_t  PersonA2BUnknown;                   // 用户A发给用户B的未识别的包数
    uint32_t  PersonB2AUnknown;                   // 用户B发给用户A的未识别的包数
    uint32_t  PersonA2B_Stun_Packets;             // 用户A发给用户B的Stun包数
    uint32_t  PersonB2A_Stun_Packets;             // 用户B发给用户A的Stun包数
    uint32_t  PersonA2BTransBytes;                // 用户A发给用户B字节总数
    uint32_t  PersonB2ATransBytes;                // 用户B发给用户A字节总数
    uint32_t  PersonFirstActiveTime;              // 用户加入语音会话的时间
    uint32_t  PersonLastActiveTime;               // 用户的上次活跃时间
    uint32_t  PersonRingTime;                     // 响铃时长
    uint8_t   PersonIsAnswered;                   // 是否被接听

    /* stun信息 */
    union     {int ipv4;char ipv6[16];}stun_ip[2];// stun中涉及的公网IP      (下标对应 0:userA，1:userB)
    uint16_t  stun_port[2];                       // stun中涉及的公网Port    (下标对应 0:userA，1:userB)
    uint16_t  stun_type[2];                       // stun类型 (ICE, TURN ……)(下标对应 0:userA，1:userB)
    uint8_t   stun_userA[10];                     // 用户名A
    uint8_t   stun_userB[10];                     // 用户名B
    // 可能还要添加stun的字段；
} ST_SkypeMediaSessionAlive;

// ZOOM 会议人员信息
typedef struct ST_ZOOM
{
    // 建联信息, 必须置于消息体的头部
    ST_trailer trailer;

    /* 会话信息 */
    uint8_t   SID[8];                            // 8字节，raw data
    int       ip_version;                        // IP版本
    union     {int ipv4;char ipv6[16];}client_ip;// IP报文 公网IP
    union     {int ipv4;char ipv6[16];}server_ip;// IP报文 公网IP
    uint16_t  client_port;                       // IP报文 公网 Port
    uint16_t  server_port;                       // IP报文 公网 Port
    uint8_t   isTimeout;                         // 这个人是否已经超时

    uint32_t  C2S_A_Packet;                      // 用户传输到服务器音频报文总数
    uint32_t  S2C_A_Packet;                      // 服务器传输到用户音频报文总数

    uint32_t  S2C_V_Packet;                      // 服务器传输到用户视频报文总数
    uint32_t  C2S_V_Packet;                      // 用户传输到服务器视频报文总数

    uint32_t  S2C_C_Packet;                      // 服务器传输到用户控制报文总数
    uint32_t  C2S_C_Packet;                      // 用户传输到服务器控制报文总数

    uint32_t  S2C_K_Packet;                      // 服务器传输到用户会话保持报文总数
    uint32_t  C2S_K_Packet;                      // 用户传输到服务器会话保持控文总数

    uint32_t  first;                             // 首次 时戳
    uint32_t  answered;                          // 应答 时戳
    uint32_t  last;                              // 最后 时戳
} PACKED ST_ZOOM_person;

/*=============================== wx ios wxid uin 通联相关 ============================================================*/
#define WXRELA_STR_LEN      24


/* 微信通联关系 通信协议格式 */
typedef struct ST_WXRELA
{
    // 建联信息, 必须置于消息体的头部
    ST_trailer trailer;

    uint8_t ip_version;
    union {
        uint32_t  ipv4;
        char ipv6[16];
    } client_ip;

    char wxid[32];
    char uin[32];
} PACKED ST_WXRELA;
/*=============================== wx ios wxid uin 通联相关 ============================================================*/


/* QQ 活动事件消息体 */
typedef struct ST_QQEventAlive
{
    // 建联信息, 必须置于消息体的头部
    ST_trailer trailer;

    /* 会话信息 */
    int       ip_version;                        // IP版本
    union     {int ipv4;char ipv6[16];}client_ip;// IP报文 公网IP
    union     {int ipv4;char ipv6[16];}server_ip;// IP报文 公网IP
    uint16_t  client_port;                       // IP报文 公网 Port
    uint16_t  server_port;                       // IP报文 公网 Port
    uint8_t   isTimeout;                         // 这个人是否已经超时

    /* 附加信息 */
    size_t    QQNum;                             // 8字节，QQ号码
    uint32_t  PersonC2STransPackets;             // 用户传输到服务器报文总数
    uint32_t  PersonS2CTransPackets;             // 服务器传输到用户报文总数
    uint32_t  PersonC2STransBytes;               // 用户传输到服务器字节总数
    uint32_t  PersonS2CTransBytes;               // 服务器传输到用户字节总数
    uint32_t  PersonFirstActiveTime;             // 用户首次活跃的时间
    uint32_t  PersonLastActiveTime;              // 用户的上次活跃时间

} ST_QQEventAlive;


/*=============================== wx 对端公网ip ============================================================*/
#define WXRELA_STR_LEN      24


/* 微信一对一通话对端公网ip 通信协议格式 */
typedef struct ST_WXPEERS
{
    // 建联信息, 必须置于消息体的头部
    ST_trailer trailer;

    uint8_t ip_version;
    union { uint32_t  ipv4; char ipv6[16]; } client_ip;

    union {
        uint32_t  ipv4;
        char ipv6[16];
    } peer_ip;
    uint32_t time_stamp;
    char location[256];
    char peeripstr[64];
    char clientipstr[64];
    uint16_t peerport;

    // 调试字段
    char country[64];
    char province[64];
    uint8_t ip_type;

    uint16_t a3_count;
    uint8_t is_timeout;

    char ttlstr[128];

} PACKED ST_WXPEERS;
/*=============================== wx 对端公网ip ============================================================*/

/*=============================== wx 共享实时位置 ============================================================*/
/* wx 共享实时位置 通信协议格式 */
typedef struct wx_location_shared_
{
    // 建联信息, 必须置于消息体的头部
    ST_trailer  trailer;
    uint8_t     trailer_flag;   // trailer 设置标记

    uint8_t    sessionid[8];
    uint32_t    c2s_pkts;
    uint32_t    s2c_pkts;
    uint32_t    begin_time;
    uint32_t    end_time;

    // ip 五元组
    uint8_t     ip_version;                        // IP版本
    union       {int ipv4;char ipv6[16];}client_ip;// IP报文 公网IP
    union       {int ipv4;char ipv6[16];}server_ip;// IP报文 公网IP
    uint16_t    client_port;                       // IP报文 公网 Port
    uint16_t    server_port;                       // IP报文 公网 Port
    uint8_t     tuple_flag;    // 五元组设置标记，根据框架流分发特性，五元组只需设置一次即可

    uint8_t     is_timeout;

} PACKED ST_WXLocSharing;
/*=============================== wx 共享实时位置 ============================================================*/


/*=============================== 腾讯会议  ============================================================*/
#define TENCENT_MEETING_MAX_MEMBER_COUNT 10   /*! 群通话成员最大输出数量 */

typedef struct {
    // 建联信息, 必须置于消息体的头部
    ST_trailer trailer;

    uint8_t  ip_version;
    uint32_t srcIp;
    uint8_t  srcIp6[16];
    uint32_t dstIp;
    uint8_t  dstIp6[16];

    uint16_t srcPort;
    uint16_t dstPort;
  
    uint32_t c2sPackCount;
    uint32_t c2sByteCount;
    uint32_t s2cPackCount;
    uint32_t s2cByteCount;

    uint64_t sessionId;
    char selfMeetingNum[19];
    char selfLoginNum[19];
    char selfUID[19];

    uint32_t firstActiveTime;
    uint32_t lastActiveTime;
    uint8_t  isTimeout;                         // 这个人是否已经超时
} ST_TencentMeeting;

/*=============================== 腾讯会议 ============================================================*/

/*=============================== 蝙蝠话单 ============================================================*/
typedef struct {
    // 建联信息, 必须置于消息体的头部
    ST_trailer trailer;

    uint8_t  ip_version;
    uint32_t srcIp;
    uint8_t  srcIp6[16];
    uint32_t dstIp;
    uint8_t  dstIp6[16];

    uint16_t srcPort;
    uint16_t dstPort;
  
    uint32_t c2sPackCount;
    uint32_t c2sByteCount;
    uint32_t s2cPackCount;
    uint32_t s2cByteCount;

    uint32_t sessionId[2];
    uint32_t firstActiveTime;
    uint32_t lastActiveTime;
    uint8_t  isTimeout;                         // 这个人是否已经超时
} ST_BatChat;
/*=============================== 蝙蝠话单 ============================================================*/




#define  WX_PYQ_JPG  0
#define  WX_PYQ_MP4  1
#define  WX_PYQ_DATA 2

/* handle */
typedef struct ST_wxc_context *wxc_handle;

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

/* interfaces */
int wxc_init(wxc_handle *pHandle, char *strServerIp, uint16_t port);
int wxc_fini(wxc_handle handle);
int wxc_sendMsg(wxc_handle, const unsigned char* pdata, int len, int msgType);

/* Trailer 解析接口 */
void get_eth_info(const uint8_t *payload, uint32_t payload_len, union eth_data *data, int type);
int  dpi_TrailerParser (ST_trailer* trailer, const char* data, unsigned int len, int trailerType);
int  dpi_TrailerGetMAC (ST_trailer* trailer, const char* mac,  const char* rt_name); //解析戎腾MAC
int  dpi_TrailerGetHWZZMAC(ST_trailer* trailer, const char* mac);
int  dpi_TrailerSetDev (ST_trailer* trailer, const char* str); // 设置计算板 板号
int  dpi_TrailerSetOpt (ST_trailer* trailer, const char* str); // 设置运营商 名称
int  dpi_TrailerSetArea(ST_trailer* trailer, const char* str); // 设置省市名 限32字节, 用于区分跨地域DPI节点
int  dpi_TrailerECI    (ST_trailer* trailer, char* str, int size); // ECI 转换
int  dpi_TrailerUpdateTS(ST_trailer* trailer);                 // 更新消息的时间戳
void dpi_TrailerDump   (ST_trailer* trailer);
#ifdef __cplusplus
}
#endif /* __cplusplus */
#endif /*  */
