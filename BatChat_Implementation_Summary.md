# WxcsBatChatAV 实现总结

## 概述
根据 ST_BatChatAV 结构体，模仿 WxcsTencentMeeting 在 wxcs 中添加了 WxcsBatChatAV 的解析输出模块，支持蝙蝠音视频通话的个人/群组和音频/视频通话类型识别。

## 实现的文件和修改

### 1. 消息类型定义 (include/wxcs_def.h)
- 添加了新的消息类型 `WXCS_BAT_CHATAV = 152`
- 修改了结构体名称为 `ST_BatChatAV`，并添加了新字段

### 2. Person 类实现 (wxcs/wxcs_person.h & wxcs/wxcs_person.cpp)
- 创建了 `WxcsBatChatAVPerson` 类，继承自 `WxcsPerson`
- 实现了构造函数，接受 `ST_BatChatAV*` 参数
- 实现了 `toStrRecord()` 方法用于生成 TBL 记录
- 实现了 `toStrBlankRecord()` 静态方法用于填充空记录

#### 主要成员变量：
```cpp
uint32_t  sessionId[2];            // 会话SessionID
uint8_t   sessionType;             // 0为"个人"，1为"群组"
uint8_t   callType;                // 0为"音频"，1为"视频"
uint32_t  c2sPackCount;            // 客户端到服务器包数
uint32_t  c2sByteCount;            // 客户端到服务器字节数
uint32_t  s2cPackCount;            // 服务器到客户端包数
uint32_t  s2cByteCount;            // 服务器到客户端字节数
uint32_t  firstActiveTime;         // 首次活跃时间
uint32_t  lastActiveTime;          // 最后活跃时间
uint8_t   isTimeout;               // 是否已经超时
```

### 3. Session 特化模板 (wxcs/wxcs_session.h & wxcs/wxcs_session.cpp)
- 为 `WxcsBatChatAVPerson` 添加了 `WxcsSession` 特化模板
- 实现了 `toStrRecordLine()` 方法用于生成会话记录
- 实现了 `getPersonList()` 方法用于获取人员列表
- 实现了 `getColumnList()` 方法用于生成字段列表，包含新的 sessionType 和 callType 字段
- 添加了特殊的 `wasDeadSession()` 超时检测逻辑，支持 `isTimeout` 字段

### 4. 服务器处理逻辑 (wxcs/wxcs_server.h & wxcs/wxcs_server.cpp)
- 添加了 `ProcessBatChatAV()` 方法处理蝙蝠音视频通话消息
- 在消息分发器 `onMessage()` 中添加了 `WXCS_BAT_CHATAV` 的处理分支
- 添加了 session keeper 和 tbl writer 成员变量：
  - `batChatAVSessionKeeper_`
  - `batChatAVTblWriter_`

#### 智能会话类型识别：
- 根据 `sessionType` 和 `callType` 自动确定 wxcs 会话类型：
  - 个人音频：`WXCS_SESSION_CHAT_SINGLE_AUDIO`
  - 个人视频：`WXCS_SESSION_CHAT_SINGLE_VIDEO`
  - 群组音频：`WXCS_SESSION_CHAT_GROUP_AUDIO`
  - 群组视频：`WXCS_SESSION_CHAT_GROUP_VIDEO`

#### 超时检测机制：
- 添加了 `checkBatChatAVSessionTimeout()` 方法
- 添加了 `onRemveBatChatAVSession()` 回调方法
- 添加了 `BatChatAVTblWriteTimeout()` TBL 写入超时检测

### 5. 配置支持
支持以下配置项（在 wxcs.conf 中）：
- `BAT_CHATAV_SESSION_TIMEOUT_IN_SECOND` - 会话超时时间
- `BAT_CHATAV_HASH_TABLE_SIZE` - 哈希表大小
- `BAT_CHATAV_TBL_FILE_LINE` - TBL 文件行数限制

## 处理流程

1. **消息接收**: 当收到 `WXCS_BAT_CHATAV` 类型的消息时，调用 `ProcessBatChatAV()`
2. **Person 创建**: 根据 `ST_BatChatAV` 结构体创建 `WxcsBatChatAVPerson` 对象
3. **会话类型识别**: 根据 `sessionType` 和 `callType` 自动确定会话类型并传入 newSession
4. **会话管理**: 使用 sessionId 作为会话标识，查找或创建会话
5. **超时检测**: 定期检查会话超时，支持 `isTimeout` 字段加速超时
6. **TBL 输出**: 超时的会话写入 TBL 文件

## 输出字段

TBL 文件包含以下字段：
- 建联信息（继承自 WxcsPerson）
- SessionID（格式：sessionId[0]_sessionId[1]）
- SessionType（0为"个人"，1为"群组"）
- CallType（0为"音频"，1为"视频"）
- 包统计信息（c2sPackCount, c2sByteCount, s2cPackCount, s2cByteCount）
- 时间信息（firstActiveTime, lastActiveTime）

## 编译状态
✅ 编译成功，无错误

## 测试建议
1. 配置相关的配置项（BAT_CHATAV_*）
2. 发送 ST_BatChatAV 消息测试消息处理
3. 测试不同的 sessionType（0=个人，1=群组）和 callType（0=音频，1=视频）组合
4. 验证会话类型自动识别是否正确
5. 验证 TBL 文件生成和字段正确性
6. 测试超时机制是否正常工作
