#ifndef DPI_HTTP_H
#define DPI_HTTP_H

#include <netinet/in.h>
#include <glib.h>
#include <string.h>
#include <ctype.h>
#include <sys/time.h>

#include "dpi_proto_ids.h"
#include "dpi_tbl_log.h"
#include "dpi_log.h"
#include "dpi_common.h"
#include "dpi_detect.h"
#include "dpi_dissector.h"



#define HTTP_CONTENT_LEN_MAX        655350
#define HTTP_UNKNOWN_LINE_NUM_MAX   50
#define HTTP_MAX_URI_LEN            1024

#define CHUNK_SIZE_MAX              32

#define HTTP_NEED_MORE -2
#define HTTP_ERROR     -1


#define CPU_LEN     64
#define OS_LEN      64
#define TYPE_LEN    20
#define LOGIN_LEN   128

enum _http_index_em{
    EM_HTTP_METHOD,
    EM_HTTP_URI,
    EM_HTTP_VERSION,
    EM_HTTP_STATUS,
    EM_HTTP_RESPONSESTATUS,
    EM_HTTP_CACHE_CONTROL,
    EM_HTTP_CONNECTION,
    EM_HTTP_COOKIE,
    EM_HTTP_COOKIE2,
    EM_HTTP_DATE,
    EM_HTTP_PRAGMA,
    EM_HTTP_TRAILER,
    EM_HTTP_TRANSFER_ENCODING,
    EM_HTTP_UPGRADE,
    EM_HTTP_VIA,
    EM_HTTP_WARNING,
    EM_HTTP_ACCEPT,
    EM_HTTP_ACCEPT_CHARSET,
    EM_HTTP_ACCEPT_ENCODING,
    EM_HTTP_ACCEPT_LANGUAGE,
    EM_HTTP_AUTHORIZATION,
    EM_HTTP_EXPECT,
    EM_HTTP_FROM,
    EM_HTTP_HOST,
    EM_HTTP_IF_MATCH,
    EM_HTTP_IF_MODIFIED_SINCE,
    EM_HTTP_IF_NONE_MATCH,
    EM_HTTP_IF_RANGE,
    EM_HTTP_IF_UNMODIFIED_SINCE,
    EM_HTTP_MAX_FORWARDS,
    EM_HTTP_PROXY_AUTHORIZATION,
#ifndef   DPI_DBZZ
    EM_HTTP_PROXY_TYPE,
    EM_HTTP_PROXY_LOGIN,
    EM_HTTP_PROXY_AUTHORIZATION_INFO,
#endif
    EM_HTTP_RANGE,
    EM_HTTP_REFERER,
    EM_HTTP_TE,
    EM_HTTP_USER_AGENT,
#ifndef   DPI_DBZZ
    EM_HTTP_PROXY_PROXYAGENT,
    EM_HTTP_USER_CPU,
    EM_HTTP_USER_OS,
#endif
    EM_HTTP_ACCEPT_RANGES,
    EM_HTTP_AGE,
    EM_HTTP_ETAG,
    EM_HTTP_LOCATION,
    EM_HTTP_PROXY_AUTHENTICATE,
#ifndef   DPI_DBZZ
    EM_HTTP_INQUIRY_TYPE,
    EM_HTTP_PROXY_CONNECT_HOST,
    EM_HTTP_PROXY_CONNECT_PORT,
#endif
    EM_HTTP_RETRY_AFTER,
    EM_HTTP_SERVER,
    EM_HTTP_VARY,
    EM_HTTP_WWW_AUTHENTICATE,
    EM_HTTP_ALLOW,
    EM_HTTP_CONTENT_ENCODING,
    EM_HTTP_CONTENT_LANGUAGE,
    EM_HTTP_CONTENT_LENGTH,
    EM_HTTP_CONTENT_LOCATION,
    EM_HTTP_CONTENT_MD5,
    EM_HTTP_CONTENT_RANGE,
    EM_HTTP_CONTENT_TYPE,
    EM_HTTP_EXPIRES,
    EM_HTTP_LAST_MODIFIED,
    EM_HTTP_X_FORWARDED_FOR,
    EM_HTTP_SET_COOKIE,
    EM_HTTP_SET_COOKIE2,
    EM_HTTP_DNT,
    EM_HTTP_X_POWERED_BY,
    EM_HTTP_P3P,
#ifndef   DPI_DBZZ
    EM_HTTP_GET_SVR_REQ_URL,    //客户端获取服务器请求的 URL
    EM_HTTP_ENTITY_TITLE_HEAD,    //实体标题首部
#endif
    EM_HTTP_H_A_NUMBER,
    EM_HTTP_H_X_NUMBER,
    

    EM_HTTP_K00,
    EM_HTTP_V00,
    EM_HTTP_K01,
    EM_HTTP_V01,
    EM_HTTP_K02,
    EM_HTTP_V02,
    EM_HTTP_K03,
    EM_HTTP_V03,
    EM_HTTP_K04,
    EM_HTTP_V04,
    EM_HTTP_K05,
    EM_HTTP_V05,
    EM_HTTP_K06,
    EM_HTTP_V06,
    EM_HTTP_K07,
    EM_HTTP_V07,
    EM_HTTP_K08,
    EM_HTTP_V08,
    EM_HTTP_K09,
    EM_HTTP_V09,
    EM_HTTP_K10,
    EM_HTTP_V10,
    EM_HTTP_K11,
    EM_HTTP_V11,
    EM_HTTP_K12,
    EM_HTTP_V12,
    EM_HTTP_K13,
    EM_HTTP_V13,
    EM_HTTP_K14,
    EM_HTTP_V14,
    EM_HTTP_K15,
    EM_HTTP_V15,
    EM_HTTP_K16,
    EM_HTTP_V16,
    EM_HTTP_K17,
    EM_HTTP_V17,
    EM_HTTP_K18,
    EM_HTTP_V18,
    EM_HTTP_K19,
    EM_HTTP_V19,
    EM_HTTP_K20,
    EM_HTTP_V20,
    EM_HTTP_K21,
    EM_HTTP_V21,
    EM_HTTP_K22,
    EM_HTTP_V22,
    EM_HTTP_K23,
    EM_HTTP_V23,
    EM_HTTP_K24,
    EM_HTTP_V24,
    EM_HTTP_K25,
    EM_HTTP_V25,
    EM_HTTP_K26,
    EM_HTTP_V26,
    EM_HTTP_K27,
    EM_HTTP_V27,
    EM_HTTP_K28,
    EM_HTTP_V28,
    EM_HTTP_K29,
    EM_HTTP_V29,
    EM_HTTP_K30,
    EM_HTTP_V30,
    EM_HTTP_K31,
    EM_HTTP_V31,
    EM_HTTP_K32,
    EM_HTTP_V32,
    EM_HTTP_K33,
    EM_HTTP_V33,
    EM_HTTP_K34,
    EM_HTTP_V34,
    EM_HTTP_K35,
    EM_HTTP_V35,
    EM_HTTP_K36,
    EM_HTTP_V36,
    EM_HTTP_K37,
    EM_HTTP_V37,
    EM_HTTP_K38,
    EM_HTTP_V38,
    EM_HTTP_K39,
    EM_HTTP_V39,
    EM_HTTP_K40,
    EM_HTTP_V40,
    EM_HTTP_K41,
    EM_HTTP_V41,
    EM_HTTP_K42,
    EM_HTTP_V42,
    EM_HTTP_K43,
    EM_HTTP_V43,
    EM_HTTP_K44,
    EM_HTTP_V44,
    EM_HTTP_K45,
    EM_HTTP_V45,
    EM_HTTP_K46,
    EM_HTTP_V46,
    EM_HTTP_K47,
    EM_HTTP_V47,
    EM_HTTP_K48,
    EM_HTTP_V48,
    EM_HTTP_K49,
    EM_HTTP_V49,
    EM_HTTP_K50,
    EM_HTTP_V50,
    EM_HTTP_K51,
    EM_HTTP_V51,
    EM_HTTP_K52,
    EM_HTTP_V52,
    EM_HTTP_K53,
    EM_HTTP_V53,
    EM_HTTP_K54,
    EM_HTTP_V54,
    EM_HTTP_K55,
    EM_HTTP_V55,
    EM_HTTP_K56,
    EM_HTTP_V56,
    EM_HTTP_K57,
    EM_HTTP_V57,
    EM_HTTP_K58,
    EM_HTTP_V58,
    EM_HTTP_K59,
    EM_HTTP_V59,
    EM_HTTP_K60,
    EM_HTTP_V60,
    EM_HTTP_K61,
    EM_HTTP_V61,
    EM_HTTP_K62,
    EM_HTTP_V62,
    EM_HTTP_K63,
    EM_HTTP_V63,
    EM_HTTP_K64,
    EM_HTTP_V64,
    EM_HTTP_K65,
    EM_HTTP_V65,
    EM_HTTP_K66,
    EM_HTTP_V66,
    EM_HTTP_K67,
    EM_HTTP_V67,
    EM_HTTP_K68,
    EM_HTTP_V68,
    EM_HTTP_K69,
    EM_HTTP_V69,
    EM_HTTP_K70,
    EM_HTTP_V70,
    EM_HTTP_K71,
    EM_HTTP_V71,
    EM_HTTP_K72,
    EM_HTTP_V72,
    EM_HTTP_K73,
    EM_HTTP_V73,
    EM_HTTP_K74,
    EM_HTTP_V74,
    EM_HTTP_K75,
    EM_HTTP_V75,
    EM_HTTP_K76,
    EM_HTTP_V76,
    EM_HTTP_K77,
    EM_HTTP_V77,
    EM_HTTP_K78,
    EM_HTTP_V78,
    EM_HTTP_K79,
    EM_HTTP_V79,
    EM_HTTP_K80,
    EM_HTTP_V80,
    EM_HTTP_K81,
    EM_HTTP_V81,
    EM_HTTP_K82,
    EM_HTTP_V82,
    EM_HTTP_K83,
    EM_HTTP_V83,
    EM_HTTP_K84,
    EM_HTTP_V84,
    EM_HTTP_K85,
    EM_HTTP_V85,
    EM_HTTP_K86,
    EM_HTTP_V86,
    EM_HTTP_K87,
    EM_HTTP_V87,
    EM_HTTP_K88,
    EM_HTTP_V88,
    EM_HTTP_K89,
    EM_HTTP_V89,
    EM_HTTP_K90,
    EM_HTTP_V90,
    EM_HTTP_K91,
    EM_HTTP_V91,
    EM_HTTP_K92,
    EM_HTTP_V92,
    EM_HTTP_K93,
    EM_HTTP_V93,
    EM_HTTP_K94,
    EM_HTTP_V94,
    EM_HTTP_K95,
    EM_HTTP_V95,
    EM_HTTP_K96,
    EM_HTTP_V96,
    EM_HTTP_K97,
    EM_HTTP_V97,
    EM_HTTP_K98,
    EM_HTTP_V98,
    EM_HTTP_K99,
    EM_HTTP_V99,

    EM_HTTP_MAX
};



struct http_unknown_line {
    uint32_t key_len;
    uint32_t val_len;
    const uint8_t *key_ptr;
    const uint8_t *val_ptr;
};


struct http_request_info {
    GHashTable *table;
    uint32_t   header_num;
    uint32_t   empty_line_position;
    PROTOCOL_HEAD_DEF(head_line)
    PROTOCOL_HEAD_DEF(version)
    PROTOCOL_HEAD_DEF(code)
    PROTOCOL_HEAD_DEF(response_code)
    PROTOCOL_HEAD_DEF(uri)
    PROTOCOL_HEAD_DEF(method)
    PROTOCOL_HEAD_DEF(content)
    char       uaCPU[CPU_LEN];
    char       uaOS[OS_LEN];
    char       proxy_login[LOGIN_LEN];
    char       proxy_type[TYPE_LEN];
    char       inquiry_type[TYPE_LEN];
    char       filename[COMMON_FILE_PATH];
    
    uint8_t    action;
};

struct http_cache
{
    char     *cache;
    int       cache_size;
    int       cache_hold;
};


struct http_file{
    FILE    *fp;
    char    file_name[COMMON_FILE_PATH];
};

struct http_session
{
    uint8_t is_pic;
    int     direction[2];
    char    host[256];
    char   *uri;
    struct  http_cache  cache[2];
    uint8_t has_sub;                // http 协议已经解析子业务，解析子业务之后 http 不再输出 tbl

    uint8_t is_batchat_flag;
    struct {
        char send_uid[64];
        char recive_uid[64];
        char file_time[32];
        char filetype[16];
        uint32_t flow_post_count;
        uint8_t cmd_id;
        char source_filename[128];
        char local_filename[256];
        FILE *fp;
    } batchat_info;

    /*!
     * 微信群头像消息是从http请求中获得，而头像图片是在响应中，因此无法在单次消息
     * 发送中即发送消息本身又发送图像数据，这里用于记录上次消息的状态
     */
    struct {
        char session_url[1024]; // 用于在聚合端确定会话
        char pic_uri[256];
    } wx_group_head_last_status;
};




typedef struct http_sub_attr_
{
    // wx 群头像属性
    uint8_t send_content;
    uint8_t is_request;
}HttpSubAttr;

// http 子业务处理模块
int http_sub(struct flow_info *flow, int direction, struct http_request_info *line_info, HttpSubAttr * attr);
void http_douyin(struct flow_info *flow, int direction, const uint8_t *payload, 
                 const uint32_t payload_len, struct http_request_info *line_info);
void http_alipay(struct flow_info *flow, int direction, const uint8_t *payload,
                 const uint32_t payload_len, struct http_request_info *line_info);
void http_batchat(struct flow_info *flow, int direction, const uint8_t *payload,
                 const uint32_t payload_len, struct http_request_info *line_info);
int flow_batchat_finish(struct flow_info *flow);
int write_http_weixin_pyq(struct flow_info *flow, int direction,  struct http_request_info *line_info);
void write_http_log_v51(struct flow_info *flow, int direction, struct http_request_info *line_info, struct tbl_log *log_ptr);
int  write_weixin_pyq_log_by_recorder(struct flow_info *flow, int direction, precord_t *record);
void write_wx_http_post_tbl(struct flow_info *flow, int direction, const uint8_t *payload, const uint32_t payload_len,
    struct http_request_info *line_info);
/* 线程初始化 */
void init_wx_gh_thread(void);

// void _find_hash_write_log_delete(struct http_request_info *line_info, const char *header_name, struct tbl_log *log_ptr, int *idx)

#endif

