#include <stdint.h>
#include <stdlib.h>
#include <sys/time.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <netinet/in.h>
#include <stdio.h>
#include <arpa/inet.h>
#include <rte_mempool.h>

#include <string.h>

#include "glib.h"

#include "dpi_log.h"
#include "dpi_proto_ids.h"
#include "dpi_tbl_log.h"
#include "dpi_tbl_record_log.h"
#include "dpi_typedefs.h"
#include "dpi_common.h"
#include "dpi_tcp_reassemble.h"
#include "dpi_dissector.h"
#include "openssl/md5.h"
#include "dpi_protorecord.h"
#include "dpi_http.h"

extern struct global_config g_config;
extern struct rte_mempool *tbl_log_mempool;
extern struct rte_mempool *tbl_log_record_mempool;

typedef enum _em_batchat_type{
    EM_BATCHAT_SEND_UID,
    EM_BATCHAT_RECIVE_UID,
    EM_BATCHAT_COMMAND,
    EM_BATCHAT_FILE_TIME,
    EM_BATCHAT_SOURCE_FILENAME,
    EM_BATCHAT_FILETYPE,
    EM_BATCHAT_LOCAL_FILENAME,

    EM_BATCHAT_MAX
}em_batchat_type;

dpi_field_table batchat_array_f[] = {
    DPI_FIELD_D(EM_BATCHAT_SEND_UID,                   EM_F_TYPE_STRING,                 "sendUID"),
    DPI_FIELD_D(EM_BATCHAT_RECIVE_UID,                 EM_F_TYPE_STRING,                 "reciveUID"),
    DPI_FIELD_D(EM_BATCHAT_COMMAND,                    EM_F_TYPE_STRING,                 "command"),
    DPI_FIELD_D(EM_BATCHAT_FILE_TIME,                  EM_F_TYPE_STRING,                 "fileTime"),
    DPI_FIELD_D(EM_BATCHAT_SOURCE_FILENAME,            EM_F_TYPE_STRING,                 "sourceFilename"),
    DPI_FIELD_D(EM_BATCHAT_FILETYPE,                   EM_F_TYPE_STRING,                 "filetype"),
    DPI_FIELD_D(EM_BATCHAT_LOCAL_FILENAME,             EM_F_TYPE_STRING,                 "localFilename"),
};

enum _cmd_index_em {
    EM_P_SEND_PIC,
    EM_P_SEND_AMR,
    EM_P_SEND_FILE,
    EM_P_RECV_PIC,
    EM_P_RECV_AMR,
    EM_P_RECV_FILE,
    EM_G_RECV_PIC,
    EM_G_RECV_AMR,
    EM_G_RECV_FILE,
    EM_C_VIEW,
};
static const struct int_to_string batchat_command[] = {
    {EM_P_SEND_PIC,    "个人发送图片消息"},
    {EM_P_SEND_AMR,    "个人发送语音消息"},
    {EM_P_SEND_FILE,   "个人发送文件消息"},
    {EM_P_RECV_PIC,    "个人接收图片消息"},
    {EM_P_RECV_AMR,    "个人接收语音消息"},
    {EM_P_RECV_FILE,   "个人接收文件消息"},
    {EM_G_RECV_PIC,    "群接收图片消息"},
    {EM_G_RECV_AMR,    "群接收语音消息"},
    {EM_G_RECV_FILE,   "群接收文件消息"},
    {EM_C_VIEW,        "浏览圈子"},
    {0,           NULL}
};

static int write_batchat_log_by_recorder(struct flow_info *flow, int direction, precord_t *record) {
    int idx = 0, i;
    struct tbl_log_record *log_ptr;
    const char *str = NULL;

    if (rte_mempool_get(tbl_log_record_mempool, (void **)&log_ptr) < 0) {
      DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_record_mempool");
      return PKT_OK;
    }
    write_tbl_log_common_by_record(flow,direction,record);
    log_ptr->record             = record;
    log_ptr->thread_id          = flow->thread_id;
    precord_layer_move_cursor(record, "batchat");

    if(tbl_record_log_enqueue(log_ptr) != 1) {
        ya_destroy_record(record);
        rte_mempool_put(tbl_log_record_mempool, (void *)log_ptr);
    }
    record = NULL;
    struct rte_mempool *pool = NULL;
    uint32_t pool_len = 0;


    return 0;
}

static void write_batchat_log(struct flow_info *flow, int direction, struct http_session *session)
{
    if(!flow || !session)
        return;

    precord_t *batchat_record = ya_create_record("batchat");
    ya_fvalue_t *new_value = NULL;
    new_value = ya_fvalue_new_stringn(YA_FT_STRING, session->batchat_info.source_filename, strlen(session->batchat_info.source_filename));
    dpi_precord_fvalue_put_by_name(batchat_record, "sourceFilename", new_value);
    new_value = ya_fvalue_new_stringn(YA_FT_STRING, session->batchat_info.send_uid, strlen(session->batchat_info.send_uid));
    dpi_precord_fvalue_put_by_name(batchat_record, "sendUID", new_value);
    new_value = ya_fvalue_new_stringn(YA_FT_STRING, session->batchat_info.recive_uid, strlen(session->batchat_info.recive_uid));
    dpi_precord_fvalue_put_by_name(batchat_record, "reciveUID", new_value);

    char *cmd_str = val_to_string(session->batchat_info.cmd_id, batchat_command);
    if(cmd_str){
        new_value = ya_fvalue_new_stringn(YA_FT_STRING, cmd_str, strlen(cmd_str));
        dpi_precord_fvalue_put_by_name(batchat_record, "command", new_value);
    }

    new_value = ya_fvalue_new_stringn(YA_FT_STRING, session->batchat_info.file_time, strlen(session->batchat_info.file_time));
    dpi_precord_fvalue_put_by_name(batchat_record, "fileTime", new_value);
    new_value = ya_fvalue_new_stringn(YA_FT_STRING, session->batchat_info.filetype, strlen(session->batchat_info.filetype));
    dpi_precord_fvalue_put_by_name(batchat_record, "filetype", new_value);
    new_value = ya_fvalue_new_stringn(YA_FT_STRING, session->batchat_info.local_filename, strlen(session->batchat_info.local_filename));
    dpi_precord_fvalue_put_by_name(batchat_record, "localFilename", new_value);

    write_batchat_log_by_recorder(flow, direction, batchat_record);
}

void http_batchat(struct flow_info *flow, int direction, const uint8_t *payload,
                 const uint32_t payload_len, struct http_request_info *line_info)
{
    if (g_config.protocol_switch[PROTOCOL_BATCHAT] == 0)
    {
        return ;
    }
    struct http_session *session = (struct http_session *)flow->app_session;
    if(!session)
        return;

    gpointer _value;
    struct header_value *value;
    char buff[256] = {0};
    if(direction == FLOW_DIR_SRC2DST) {
        _value = g_hash_table_lookup(line_info->table, "uid");
        if (_value) {
            value = (struct header_value *)_value;
            if(value->ptr && value->len){
                char *uid_ptr = NULL;
                if(!memcmp(line_info->PROTOCOL_VAL_PTR(method), "GET ", 4))
                    uid_ptr = session->batchat_info.recive_uid;
                else
                    uid_ptr = session->batchat_info.send_uid;
                uint32_t uid_max_len = sizeof(session->batchat_info.send_uid);
                bzero(uid_ptr, uid_max_len);
                memcpy(uid_ptr, (char *)value->ptr, value->len<(uid_max_len-1)?value->len:(uid_max_len-1));
            }
        }

        _value = g_hash_table_lookup(line_info->table, "timestamp");
        if (_value) {
            value = (struct header_value *)_value;
            if(value->ptr && value->len){
                char *time_ptr = session->batchat_info.file_time;
                uint32_t time_max_len = sizeof(session->batchat_info.file_time);
                bzero(time_ptr, time_max_len);
                memcpy(time_ptr, (char *)value->ptr, value->len<(time_max_len-1)?value->len:(time_max_len-1));
                timet_to_datetime(atol(time_ptr)/1000, time_ptr, time_max_len);
            }
        }

        if(!memcmp(line_info->PROTOCOL_VAL_PTR(method), "GET ", 4)){
            char *p1 = memchr((char*)line_info->PROTOCOL_VAL_PTR(uri), '.', line_info->PROTOCOL_VAL_LEN(uri));
            char *p2 = memchr((char*)line_info->PROTOCOL_VAL_PTR(uri), '?', line_info->PROTOCOL_VAL_LEN(uri));
            if(p1 && p2 && p2>p1){
                uint32_t suffix_len = p2-p1-1;
                char *filetype_ptr = session->batchat_info.filetype;
                uint32_t filetype_max_len = sizeof(session->batchat_info.filetype);
                bzero(filetype_ptr, filetype_max_len);
                memcpy(filetype_ptr, p1+1, suffix_len<filetype_max_len?suffix_len:filetype_max_len);
                if(!memcmp(filetype_ptr, "bgf", suffix_len)){
                    snprintf(filetype_ptr, filetype_max_len, "jpg");
                    if(!memcmp(session->host, "groupmsg.oss.baticq.com", 23))
                        session->batchat_info.cmd_id = EM_G_RECV_PIC;
                    else if(!memcmp(session->host, "circle.baticq.com", 17))
                        session->batchat_info.cmd_id = EM_C_VIEW;
                    else
                        session->batchat_info.cmd_id = EM_P_RECV_PIC;
                } else if(!memcmp(filetype_ptr, "amr", suffix_len)) {
                    if(!memcmp(session->host, "groupmsg.oss.baticq.com", 23))
                        session->batchat_info.cmd_id = EM_G_RECV_AMR;
                    else if(!memcmp(session->host, "circle.baticq.com", 17))
                        session->batchat_info.cmd_id = EM_C_VIEW;
                    else
                        session->batchat_info.cmd_id = EM_P_RECV_AMR;
                } else {
                    if(!memcmp(session->host, "groupmsg.oss.baticq.com", 23))
                        session->batchat_info.cmd_id = EM_G_RECV_FILE;
                    else if(!memcmp(session->host, "circle.baticq.com", 17))
                        session->batchat_info.cmd_id = EM_C_VIEW;
                    else
                        session->batchat_info.cmd_id = EM_P_RECV_FILE;
                }
            }
        }else if (line_info->PROTOCOL_VAL_LEN(content) > 120){
            uint8_t is_boundary = 0;
            int index = 0, offset = 0;
            int write_size = 0;
            char boundary[64] = {0};
            _value = g_hash_table_lookup(line_info->table, "content-type");
            if (_value) {
                char transfer[128] = {0};
                value = (struct header_value *)_value;
                memcpy(transfer, value->ptr, value->len >= sizeof(transfer) ? sizeof(transfer) - 1 : value->len);
                if(strcasestr(transfer, "multipart/form-data; boundary")){
                   is_boundary = 1;
                   char *boundary_ptr = memmem(transfer, strlen(transfer), "boundary=", 9);
                   if(boundary_ptr){
                       snprintf(boundary, sizeof(boundary), "%s", boundary_ptr+9);
                   }
                }

                if(is_boundary) {
                    index = line_info->PROTOCOL_VAL_PTR(content) - payload;
                    //找到实体文件开头
                    offset = _find_empty_line(payload + index, payload_len - index);
                    if(offset > 0){
                        index += offset;
                        if(payload[index] == '\r' &&payload[index+1] == '\n' ){
                            index += 2;
                        }
                    }
                    //提取文件名
                    char *p1 = memmem(line_info->PROTOCOL_VAL_PTR(content), offset, "filename=\"", 10);
                    if(p1) {
                        char *p2 = memchr(p1+10, '"', line_info->PROTOCOL_VAL_PTR(content)+offset -(const uint8_t*)(p1+10));
                        if(p2){
                            uint32_t filename_len = p2 - (p1+10);
                            uint32_t max_len = sizeof(session->batchat_info.source_filename);
                            memcpy(session->batchat_info.source_filename, p1+10, filename_len<max_len?filename_len:max_len-1);
                        }
                    }
                    // 找到文件结尾
                    uint8_t  *file_end_pattern = memmem(payload + index, payload_len - index, boundary, strlen(boundary));
                    if (file_end_pattern == NULL) {
                      return ;
                    }
                    write_size = file_end_pattern - (payload + index);
                    //去除boundary前面的四个换行字节
                    write_size -= 4;
                }
            }
            if(!session->batchat_info.fp){
                    struct timeval tv;
                    gettimeofday(&tv, NULL);
                    char *local_filename = session->batchat_info.local_filename;
                    uint32_t max_len = sizeof(session->batchat_info.local_filename);
                    snprintf(local_filename, max_len, "%s/batchat/%s_%06ld_batchat_%03d.writing", g_config.tbl_out_dir, dpi_now(&tv.tv_sec, buff, sizeof(buff)), tv.tv_usec, flow->thread_id);
                    session->batchat_info.fp = fopen(local_filename, "w");
                    if(!session->batchat_info.fp) return;
            }

            if(is_boundary){
                if(write_size < 0) return;
                fwrite(payload+index, write_size, 1, session->batchat_info.fp);
            } else
                fwrite(line_info->PROTOCOL_VAL_PTR(content), line_info->PROTOCOL_VAL_LEN(content), 1, session->batchat_info.fp);
        }
    } else {
        //GET 响应中提取实体文件
        _value = g_hash_table_lookup(line_info->table, "etag");
        if (_value) {
            value = (struct header_value *)_value;
            if(value->ptr && value->len){
                uint32_t max_len = value->len>(sizeof(buff)-1)?(sizeof(buff)-1):value->len;
                for(int i=0,j=0; i < max_len; i++){
                    if(*(value->ptr+i) != '"') {
                        buff[j++] = *(value->ptr+i);
                    }
                }
                snprintf(session->batchat_info.source_filename, sizeof(session->batchat_info.source_filename), "%s", buff);
                if(!memcmp(line_info->PROTOCOL_VAL_PTR(code), "200", line_info->PROTOCOL_VAL_LEN(code))) {
                    bzero(session->batchat_info.local_filename, sizeof(session->batchat_info.local_filename));
                    if(line_info->PROTOCOL_VAL_PTR(content)){
                        if(line_info->PROTOCOL_VAL_LEN(content) > 21 && !memcmp(line_info->PROTOCOL_VAL_PTR(content), "BATF", 4)){
                            uint8_t suffix_len = *(line_info->PROTOCOL_VAL_PTR(content)+17);
                            memcpy(session->batchat_info.filetype, line_info->PROTOCOL_VAL_PTR(content)+18, suffix_len);
                            line_info->PROTOCOL_VAL_PTR(content) = line_info->PROTOCOL_VAL_PTR(content)+18 + suffix_len;
                            line_info->PROTOCOL_VAL_LEN(content) = line_info->PROTOCOL_VAL_LEN(content) - 18 - suffix_len;
                        }
                        struct timeval tv;
                        gettimeofday(&tv, NULL);
                        char *local_filename = session->batchat_info.local_filename;
                        uint32_t max_len = sizeof(session->batchat_info.local_filename);
                        snprintf(local_filename, max_len, "%s/batchat/%s_%06ld_batchat_%03d.%s", g_config.tbl_out_dir, dpi_now(&tv.tv_sec, buff, sizeof(buff)), tv.tv_usec, flow->thread_id, session->batchat_info.filetype);
                        FILE *fp = fopen(local_filename, "w");
                        if(fp){
                            fwrite(line_info->PROTOCOL_VAL_PTR(content), line_info->PROTOCOL_VAL_LEN(content), 1, fp);
                            fclose(fp);
                        }
                    }
                    write_batchat_log(flow, direction, session);
                }else if (!memcmp(line_info->PROTOCOL_VAL_PTR(code), "206", line_info->PROTOCOL_VAL_LEN(code))) {
                    if(line_info->PROTOCOL_VAL_LEN(content)){
                        if(!session->batchat_info.fp){
                                struct timeval tv;
                                gettimeofday(&tv, NULL);
                                char *local_filename = session->batchat_info.local_filename;
                                uint32_t max_len = sizeof(session->batchat_info.local_filename);
                                snprintf(local_filename, max_len, "%s/batchat/%s_%06ld_batchat_%03d.writing", g_config.tbl_out_dir, dpi_now(&tv.tv_sec, buff, sizeof(buff)), tv.tv_usec, flow->thread_id);
                                session->batchat_info.fp = fopen(local_filename, "w");
                                if(!session->batchat_info.fp) return;
                        }
                        _value = g_hash_table_lookup(line_info->table, "content-range");
                        if (_value) {
                            value = (struct header_value *)_value;
                            if(value->ptr && value->len){
                                //分片传输 偏移指定位置
                                char *p1 = memchr(value->ptr, ' ', value->len);
                                char *p2 = memchr(value->ptr, '-', value->len);
                                if(!p1 || !p2) return;
                                uint32_t copy_len = p2 - p1 - 1;
                                copy_len = copy_len<sizeof(buff)?copy_len:(sizeof(buff)-1);
                                memcpy(buff, p1+1, copy_len);
                                buff[copy_len] = '\0';
                                uint32_t offset = atol(buff);
                                fseek(session->batchat_info.fp, offset, SEEK_SET);
                                //图片bat封装处理
                                if(line_info->PROTOCOL_VAL_LEN(content) > 21 && !memcmp(line_info->PROTOCOL_VAL_PTR(content), "BATF", 4)){
                                    uint8_t suffix_len = *(line_info->PROTOCOL_VAL_PTR(content)+17);
                                    line_info->PROTOCOL_VAL_PTR(content) = line_info->PROTOCOL_VAL_PTR(content)+18 + suffix_len;
                                    line_info->PROTOCOL_VAL_LEN(content) = line_info->PROTOCOL_VAL_LEN(content) - 18 - suffix_len;
                                }
                                fwrite(line_info->PROTOCOL_VAL_PTR(content), line_info->PROTOCOL_VAL_LEN(content), 1, session->batchat_info.fp);
                            }
                        }
                    }
                }
            }
        } else {//POST 从响应中获取文件格式
            if(session->batchat_info.source_filename[0] != '\0' && line_info->PROTOCOL_VAL_LEN(content) > 64) {
                char *filename_start = memmem(line_info->PROTOCOL_VAL_PTR(content), line_info->PROTOCOL_VAL_LEN(content), session->batchat_info.source_filename, strlen(session->batchat_info.source_filename));
                if(!filename_start)
                    return;
                char *p1 = memchr(filename_start, '.', line_info->PROTOCOL_VAL_PTR(content)+line_info->PROTOCOL_VAL_LEN(content) - (const uint8_t*)filename_start);
                char *p2 = memchr(filename_start, ':', line_info->PROTOCOL_VAL_PTR(content)+line_info->PROTOCOL_VAL_LEN(content) - (const uint8_t*)filename_start);
                if(p1 && p2 && p2 > p1){
                    uint32_t suffix_len = p2-p1-1;
                    char *filetype_ptr = session->batchat_info.filetype;
                    uint32_t filetype_max_len = sizeof(session->batchat_info.filetype);
                    bzero(filetype_ptr, filetype_max_len);
                    memcpy(filetype_ptr, p1+1, suffix_len<filetype_max_len?suffix_len:filetype_max_len);
                    if(!memcmp(filetype_ptr, "bgf", suffix_len)){
                        snprintf(filetype_ptr, filetype_max_len, "jpg");
                        session->batchat_info.cmd_id = EM_P_SEND_PIC;
                    } else if(!memcmp(filetype_ptr, "amr", suffix_len)) {
                        session->batchat_info.cmd_id = EM_P_SEND_AMR;
                    } else {
                        session->batchat_info.cmd_id = EM_P_SEND_FILE;
                    }
                }
            }
        }
        //重置
        session->is_batchat_flag = 0;

    }

    return;
}

int flow_batchat_finish(struct flow_info *flow)
{
    if (flow->app_session) {
        struct http_session *session = (struct http_session *)flow->app_session;;
        if(session){
            if(session->batchat_info.fp){
                fclose(session->batchat_info.fp);
                session->batchat_info.fp = NULL;
                char temp_filename[256] = {0};
                snprintf(temp_filename, sizeof(temp_filename), "%s", session->batchat_info.local_filename);
                char *pos = strchr(session->batchat_info.local_filename, '.');
                if(session->batchat_info.filetype[0] != '\0')
                    snprintf(pos+1, session->batchat_info.local_filename + sizeof(session->batchat_info.local_filename)- pos- 1, "%s", session->batchat_info.filetype);
                else
                    snprintf(pos+1, session->batchat_info.local_filename + sizeof(session->batchat_info.local_filename)- pos- 1, "bin");
                rename(temp_filename, session->batchat_info.local_filename);
                write_batchat_log(flow, 0, session); 
            }
        }
    }
    return 0;
}

static void init_batchat_protorecord(void){
    write_proto_record_field_tab((dpi_record_field_table *)batchat_array_f, EM_BATCHAT_MAX, "batchat");
}

static __attribute((constructor)) void     before_init_batchat(void){
    dpi_proto_register("batchat", init_batchat_protorecord);
}

