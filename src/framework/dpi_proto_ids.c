#include "dpi_proto_ids.h"
#include "dpi_detect.h"



const char *protocol_name_array[PROTOCOL_MAX] =
{
    "UNKNOWN",
    "HTTP",
    "HTTP_QQACC",
    "HTTP_WXPH",
    "HTTP_WX_MSG",
    "WEIX<PERSON>",
    "WEIXIN_MEDIA_CHAT",
    "WEIXIN_PYQ",
    "WEIXIN_POSITION",
    "WEIXIN_GROUP_HEAD",
    "WEIXIN_GROUP_HEAD_CONTENT",
    "WEIXIN_MESSAGE",
    "WEIXIN_HTTP_POST",
    "QQ_VOIP",
    "QQ_FILE",
    "SKYPE_MEDIA_CHAT",
    "ZOOM_CONFERENCE",
    "WEIXIN_RELA",
    "WEIXIN_INFO",
    "WX_PEERS",
    "QQ_EVENT",
    "WX_LOCSHARING",
    "WEIXIN_MISC",
    "REL<PERSON><PERSON>",
    "GQ<PERSON><PERSON>_WEIXIN",
    "RTP",
    "DO<PERSON>Y<PERSON>",
    "WXID",
    "WXPAY",
    "ALIPAY",
    "TENCENT_MEETING",
    "BATCHAT",
};



struct guess_proto_data tcp_port_proto[65536];
struct guess_proto_data udp_port_proto[65536];


struct check_proto_data tcp_detection_array[PROTOCOL_MAX];
struct check_proto_data udp_detection_array[PROTOCOL_MAX];


