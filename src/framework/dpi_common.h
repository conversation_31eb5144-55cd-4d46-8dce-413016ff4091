#ifndef _DPI_COMMON_H_
#define _DPI_COMMON_H_

#include <stdint.h>
#include <stddef.h>
#include <arpa/inet.h>

#define COMMON_FILE_PATH         256
#define COMMON_FILTER_NUM        10
#define ARRAY_LEN(array) ((sizeof (array)) / (sizeof (array)[0]))

struct header_value
{
	uint8_t need_free;
	uint16_t len;
	const uint8_t *ptr;
};

struct header_tmp_value
{
    uint8_t need_free;
    uint16_t len;
	uint8_t *ptr;
};

struct int_to_string
{
	int value;
	const char *strptr;
};

/*for safe get int/string value from payload*/
struct dpi_pkt_st
{
	const uint8_t *payload;
	uint32_t payload_len;
};

#define DPI_UTF8_ARRNUM_MAX     128     // 存储 utf8 信息数组的最大长度      
typedef struct 
{
  uint8_t num;                              // 提取计数
  uint32_t offset[DPI_UTF8_ARRNUM_MAX];    // 每个 utf8 字符串的起始便宜位置
  uint32_t len[DPI_UTF8_ARRNUM_MAX];       // offset 对应的字符串长度
}DpiUtf8Info;

#define ATOMIC_ADD_FETCH(a)    __sync_add_and_fetch(a, 1)
#define ATOMIC_SUB_FETCH(a)    __sync_sub_and_fetch(a, 1)
#define ATOMIC_ADD_NUM(a,n)    __sync_add_and_fetch(a, n)
#define ATOMIC_SUB_NUM(a,n)    __sync_sub_and_fetch(a, n)
#define ATOMIC_COM_SWAP(a,o,n) __sync_val_compare_and_swap(a,o,n)
#define ATOMIC_SET(a,n)        __sync_lock_test_and_set(a,n)
#define ATOMIC_ZERO(a)         __sync_lock_release(a)

#define DPI_COMMON_BUFF_LEN  1024

#define PROTOCOL_HEAD_DEF(head_name) const uint8_t *head_name##_val_ptr; uint32_t head_name##_val_len;
// #define PROTOCOL_VAL_PTR(head_name) head_name##_val_ptr
// #define PROTOCOL_VAL_LEN(head_name) head_name##_val_len

#define HEAD_REF(head_name) const uint8_t *head_name##_val_ptr; uint32_t head_name##_val_len;
#define PROTOCOL_VAL_PTR(head_name) head_name##_val_ptr
#define PROTOCOL_VAL_LEN(head_name) head_name##_val_len

#define UNUSED(arg) do {(void)(arg); } while(0);
#define array_length(x)	(sizeof x / sizeof x[0])

#define get_uint8_t(X,O)  (*(const uint8_t *)(((const uint8_t *)X) + O))
#define get_uint16_t(X,O)  (*(const uint16_t *)(((const uint8_t *)X) + O))
#define get_uint32_t(X,O)  (*(const uint32_t *)(((const uint8_t *)X) + O))
#define get_uint64_t(X,O)  (*(const uint64_t *)(((const uint8_t *)X) + O))
// #define get_uint64_ntoh64(X,O) 
#define get_uint16_ntohs(X,O)  (ntohs(*(const uint16_t *)(((const uint8_t *)X) + O)))
#define get_uint32_ntohl(X,O)  (ntohl(*(const uint32_t *)(((const uint8_t *)X) + O)))

#define dpi_match_strprefix(payload, payload_len, str) match_prefix_str((payload), (payload_len), (str), (sizeof(str)-1))

int find_packet_line_end(const uint8_t *data, uint16_t len);
int match_prefix_str(const uint8_t *payload, size_t payload_len, const char *str, size_t str_len);

uint32_t get_token_len(const uint8_t *linep, const uint8_t *lineend, const uint8_t **next_token);
int find_blank_space(const uint8_t *start, int max_len);
int find_special_char(const uint8_t *start, int max_len, char c);
int _find_empty_line(const uint8_t *payload, uint32_t payload_len);
int _find_three_zero_position(const uint8_t *payload, uint32_t payload_len);

char *strdown_inplace(char *str);
int find_str_end_len(const uint8_t *start, uint32_t max_len);
const char *val_to_string(const int val, const struct int_to_string *vs);
int timet_to_datetime(time_t t, char *time_str, int len);
char *get_owner_path(void);

void *dpi_malloc(size_t size);
void  dpi_free(void *ptr);

// 数据复制(内部maloc实现)
void *memdup(const void *src, size_t l);

// 判定 数据是否为 HEX字符串
int isHEX(const char* p, int len);

// 判定: 是不是空
int is_zero(const unsigned char *p, size_t len);

// 计算数据UTF8编码长度
int isUTF8(const char *pData, int len);
int isGBK(const char *pData, int len);

// 获取当前时间字符串
const char *dpi_now(time_t *t, char *time_str, int buff_len);

// 转换: RAW 转HEX字符
int bintohex(const char *i, size_t l, char *p, size_t s);

// 转换: HEX 字符转RAW
int hextobin(const char *i, size_t l, char *p, size_t s);

char **str_split(char *a_str, const char a_delim);

void mkdirs(const char *dir);

void wall(const char *format, ...);

int dpi_get_uint8(struct dpi_pkt_st *pkt, uint32_t offset, uint8_t *val);
int dpi_get_be16(struct dpi_pkt_st *pkt, uint32_t offset, uint16_t *val);
int dpi_get_be24(struct dpi_pkt_st *pkt, uint32_t offset, uint32_t *val);
int dpi_get_be32(struct dpi_pkt_st *pkt, uint32_t offset, uint32_t *val);
int dpi_get_be48(struct dpi_pkt_st *pkt, uint32_t offset, uint64_t *val);
int dpi_get_be64(struct dpi_pkt_st *pkt, uint32_t offset, uint64_t *val);

int dpi_get_le16(struct dpi_pkt_st *pkt, uint32_t offset, uint16_t *val);
int dpi_get_le24(struct dpi_pkt_st *pkt, uint32_t offset, uint32_t *val);
int dpi_get_le32(struct dpi_pkt_st *pkt, uint32_t offset, uint32_t *val);
int dpi_get_le64(struct dpi_pkt_st *pkt, uint32_t offset, uint64_t *val);
int dpi_get_string_endwith_null(struct dpi_pkt_st *pkt, uint32_t offset, uint32_t len, char *val, uint32_t max_len);
int dpi_get_string_ascii(struct dpi_pkt_st *pkt, uint32_t offset, uint32_t len, char *val, uint32_t max_len);
int dpi_get_hex_string(struct dpi_pkt_st *pkt, uint32_t offset, uint32_t len, char *val, uint32_t max_len);
int dpi_get_guid(struct dpi_pkt_st *pkt, uint32_t offset, char *val, uint32_t max_len);
int dpi_strneql(struct dpi_pkt_st *pkt, uint32_t offset, const char *str, const size_t size);
int dpi_strstr(const uint8_t *search_str, uint32_t ss_len, const char* t_str, uint8_t t_len);
int is_file_exist(const char *filepath);


const char *
strncasestr(const char *s,const char *find, size_t slen);

const uint8_t* memstr(const uint8_t* long_str, const char* short_str, int length);


const char *time_to_datetime(time_t t);

const char *filetype(const char*p, int l);


#define BASE64_ENCODE_OUT_SIZE(s) ((unsigned int)((((s) + 2) / 3) * 4 + 1))
#define BASE64_DECODE_OUT_SIZE(s) ((unsigned int)(((s) / 4) * 3))

/*
 * out is null-terminated encode string.
 * return values is out length, exclusive terminating `\0'
 */
unsigned int
base64_encode(const unsigned char *in, unsigned int inlen, char *out);

/*
 * return values is out length
 */
unsigned int
base64_decode(const char *in, unsigned int inlen, unsigned char *out);


uint64_t hex_to_decimal(const char * hex, unsigned int hex_len);

int dpi_strstr_kmp(char* haystack, int h_len, char* needle);


int dpi_get_utf8_arr(const uint8_t * data, uint32_t data_len, 
                     DpiUtf8Info *info, uint32_t utf8_min_len);
typedef void (*dir_callback)(const char * file_path);
void dpi_utils_traverse_dir(const char * path, dir_callback cb);
#endif
