/****************************************************************************************
 * 文 件 名 : wxcs_session.cpp
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : root      '2019-01-14
* 编    码 : root      '2019-01-14
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#include "wxcs_session.h"
#include "wxcs_def.h"
#include "list"
#define WX_COLUMN_TEXT(columns, text, sep)     columns += std::string(text) + sep
std::string WriteTrailerCommon(int i, char sep)
{
    std::string index = (i<= 9) ? "0" + std::to_string(i) : std::to_string(i);
    std::string strColumns;

    /* trailer 公共部分 */
    WX_COLUMN_TEXT(strColumns, "Person_TAGTYPE_"          + index, sep);
    WX_COLUMN_TEXT(strColumns, "Person_TEID_"             + index, sep);
    WX_COLUMN_TEXT(strColumns, "Person_MSISDN_"           + index, sep);
    WX_COLUMN_TEXT(strColumns, "Person_IMSI_"             + index, sep);
    WX_COLUMN_TEXT(strColumns, "Person_IMEI_"             + index, sep);
    WX_COLUMN_TEXT(strColumns, "Person_TAC_"              + index, sep);
    WX_COLUMN_TEXT(strColumns, "Person_Operator_"         + index, sep);
    WX_COLUMN_TEXT(strColumns, "Person_DevName_"          + index, sep);
    WX_COLUMN_TEXT(strColumns, "Person_DevArea_"          + index, sep);

    /* trailer 恒为 独有部分 */
    WX_COLUMN_TEXT(strColumns, "Person_HW_BFLAGS_"        + index, sep);
    WX_COLUMN_TEXT(strColumns, "Person_HW_APN_"           + index, sep);
    WX_COLUMN_TEXT(strColumns, "Person_HW_NCODE_"         + index, sep);
    WX_COLUMN_TEXT(strColumns, "Person_HW_ECGI_"          + index, sep);
    WX_COLUMN_TEXT(strColumns, "Person_HW_LAC_"           + index, sep);
    WX_COLUMN_TEXT(strColumns, "Person_HW_SAC_"           + index, sep);
    WX_COLUMN_TEXT(strColumns, "Person_HW_CI_"            + index, sep);

    /* trailer 戎腾 独有部分 */
    WX_COLUMN_TEXT(strColumns, "Person_RT_PLMN_ID_"       + index, sep);
    WX_COLUMN_TEXT(strColumns, "Person_RT_ULI_"           + index, sep);
    WX_COLUMN_TEXT(strColumns, "Person_RT_BS_"            + index, sep);
    WX_COLUMN_TEXT(strColumns, "Person_RT_DNS_"           + index, sep);

    /* TCP/IP 部分 */
    WX_COLUMN_TEXT(strColumns, "PersonSrcIp_"             + index, sep);
    WX_COLUMN_TEXT(strColumns, "PersonDstIp_"             + index, sep);
    WX_COLUMN_TEXT(strColumns, "PersonSrcPort_"           + index, sep);
    WX_COLUMN_TEXT(strColumns, "PersonDstPort_"           + index, sep);

    return strColumns;
}



template<>
std::string WxcsSessionBase<WxcsZoomPerson>::getColumnList(char sep)
{
    std::string strColumns;

    WX_COLUMN_TEXT(strColumns, "DevNo"            , sep);
    WX_COLUMN_TEXT(strColumns, "LineNo"           , sep);
    WX_COLUMN_TEXT(strColumns, "SessionBegin"     , sep);
    WX_COLUMN_TEXT(strColumns, "SessionEnd"       , sep);
    WX_COLUMN_TEXT(strColumns, "IsAnswered"       , sep);
    WX_COLUMN_TEXT(strColumns, "MeetingWait"      , sep);
    WX_COLUMN_TEXT(strColumns, "MeetingDuration"  , sep);
    WX_COLUMN_TEXT(strColumns, "IsVideo"          , sep);
    WX_COLUMN_TEXT(strColumns, "PersonCount"      , sep);
    WX_COLUMN_TEXT(strColumns, "PersonList"       , sep);

    for (int i = 0; i<SESSION_PERSON_COUNT_MAX; i++)
    {
        strColumns += WriteTrailerCommon(i, sep);
        std::string index = (i<SESSION_PERSON_COUNT_MAX) ? "0" + std::to_string(i) : std::to_string(i);

        WX_COLUMN_TEXT(strColumns, "C2S_Packet_"      + index, sep);
        WX_COLUMN_TEXT(strColumns, "S2C_Packet_"      + index, sep);
        WX_COLUMN_TEXT(strColumns, "TimeEnter_"       + index, sep);
        WX_COLUMN_TEXT(strColumns, "TimeLeave_"       + index, sep);
        WX_COLUMN_TEXT(strColumns, "IsAnswered_"      + index, sep);
        WX_COLUMN_TEXT(strColumns, "MeetingWait_"     + index, sep);
        WX_COLUMN_TEXT(strColumns, "MeetingDuration_" + index, sep);
        WX_COLUMN_TEXT(strColumns, "Resv_"            + index, sep);
    }

    return strColumns;
}


/* 1 template specialization for WxcsGroupHeadPerson */
template<>
std::string WxcsSessionBase<WxcsGroupHeadPerson>::getColumnList(char sep)
{
    #define WX_COLUMN_TEXT(columns, text, sep)     columns += std::string(text) + sep

    std::string strColumns;

    WX_COLUMN_TEXT(strColumns, "DevNo"                  , sep);
    WX_COLUMN_TEXT(strColumns, "LineNo"                 , sep);
    WX_COLUMN_TEXT(strColumns, "UpdateTime"             , sep);
    WX_COLUMN_TEXT(strColumns, "GroupHeadURL"           , sep);
    WX_COLUMN_TEXT(strColumns, "PersonCount"            , sep);
    WX_COLUMN_TEXT(strColumns, "PersonList"             , sep);
    WX_COLUMN_TEXT(strColumns, "GroupHeadPicture"       , sep);
    WX_COLUMN_TEXT(strColumns, "GroupHeadPictureMD5"    , sep);

    for (int i = 0; i<SESSION_GROUP_PERSON_COUNT_MAX; i++)
    {
        strColumns += WriteTrailerCommon(i, sep);
        std::string index = (i<=9) ? "0" + std::to_string(i) : std::to_string(i);

        WX_COLUMN_TEXT(strColumns, "PersonHost_"            + index, sep);
        WX_COLUMN_TEXT(strColumns, "PersonUin_"             + index, sep);
        WX_COLUMN_TEXT(strColumns, "PersonRef_"             + index, sep);
        WX_COLUMN_TEXT(strColumns, "PersonAgent_"           + index, sep);
        WX_COLUMN_TEXT(strColumns, "PersonAcceptEncoding_"  + index, sep);
        WX_COLUMN_TEXT(strColumns, "PersonAcceptLanguage_"  + index, sep);

        WX_COLUMN_TEXT(strColumns, "PersonLastActiveTime_"  + index, sep);
        WX_COLUMN_TEXT(strColumns, "PersonResv_"            + index, sep);
    }

    return strColumns;
}

/* 3 template specialization for WxcsPositionPerson */
template<>
std::string WxcsSessionBase<WxcsPositionPerson>::getColumnList(char sep)
{
#define WX_COLUMN_TEXT(columns, text, sep)     columns += std::string(text) + sep

    std::string strColumns;

    WX_COLUMN_TEXT(strColumns, "DevNo",       sep);
    WX_COLUMN_TEXT(strColumns, "LineNo",      sep);
    WX_COLUMN_TEXT(strColumns, "UpdateTime",  sep);
    WX_COLUMN_TEXT(strColumns, "PersonCount", sep);
    WX_COLUMN_TEXT(strColumns, "PersonList",  sep);
    WX_COLUMN_TEXT(strColumns, "longitude",   sep);
    WX_COLUMN_TEXT(strColumns, "latitude",    sep);
    WX_COLUMN_TEXT(strColumns, "zoom",        sep);

    for (int i = 0; i < SESSION_POSITION_PERSON_COUNT_MAX; i++)
    {
        strColumns += WriteTrailerCommon(i, sep);
        std::string index = (i<=9) ? "0" + std::to_string(i) : std::to_string(i);

        WX_COLUMN_TEXT(strColumns, "PersonHost_"           + index, sep);
        WX_COLUMN_TEXT(strColumns, "PersonAgent_"          + index, sep);
        WX_COLUMN_TEXT(strColumns, "PersonAcceptEncoding_" + index, sep);
        WX_COLUMN_TEXT(strColumns, "PersonUrl_"            + index, sep);
        WX_COLUMN_TEXT(strColumns, "PersonLastActiveTime_" + index, sep);
        WX_COLUMN_TEXT(strColumns, "PersonResv_"           + index, sep);
    }

    return strColumns;
}

int Countchar(const char*p, int len, char c)
{
    if(NULL == p || len < 0 )
    {
        return -1;
    }

    int i = 0;
    int count =0;
    for(i = 0; i<len; i++)
    {
        if(c == p[i])
        {
            count++;
        }
    }
    return count;
}

std::string WxcsSession<WxcsGroupHeadPerson>::toStrRecordLine(char sep) const
{
    std::string strLine;

    // 写入头部信息
    CONS_RECORD_FIELD_TEXT(strLine , "wxgh"                                     , sep); // DevNO
    CONS_RECORD_FIELD_TEXT(strLine , "007"                                      , sep); // LineNO
    CONS_RECORD_FIELD_TIME(strLine , sessionStartTime_                          , sep); // SessionStartTime
    CONS_RECORD_FIELD(strLine      , getSessionId()                             , sep); // URL
    CONS_RECORD_FIELD_NUM(strLine  , personMap_.size()                          , sep); // PersonCount
    CONS_RECORD_FIELD(strLine      , getPersonList()                            , sep); // PersonList
    CONS_RECORD_FIELD(strLine      , groupHeadPicture                           , sep); // GroupHeadPicture
    CONS_RECORD_FIELD(strLine      , groupHeadPictureMd5                        , sep); // GroupHeadPictureMD5

    //写入每个人的数据
    int iChecked = 0;
    for (auto iter = personMap_.begin();
         iter != personMap_.end() && iChecked < SESSION_GROUP_PERSON_COUNT_MAX;
         iter++, iChecked++)
    {
        strLine += iter->second->toStrRecord(sep);
    }

    // 接着写入空白行
    for (; iChecked < SESSION_GROUP_PERSON_COUNT_MAX; iChecked++)
    {
        strLine += WxcsGroupHeadPerson::toStrBlankRecord(sep);
    }

    // 检测分隔符数量
    int wc = Countchar(strLine.c_str(), strLine.length(), '|');
    if(wc < 1006)
    {
        LOG_DEF->warn("DROP_WXGH:Split char count {}", wc);
        return "";
    }

    return strLine;
}


/* 1 template specialization for WxcsSkypePerson */
template<>
std::string WxcsSessionBase<WxcsSkypePerson>::getColumnList(char sep)
{
    std::string strColumns;

    WX_COLUMN_TEXT(strColumns, "DevNo"            , sep);
    WX_COLUMN_TEXT(strColumns, "LineNo"           , sep);
    WX_COLUMN_TEXT(strColumns, "CapDate"          , sep);
    WX_COLUMN_TEXT(strColumns, "SessionType"      , sep);
    WX_COLUMN_TEXT(strColumns, "SessionStartTime" , sep);
    WX_COLUMN_TEXT(strColumns, "SessionStopTime"  , sep);
    WX_COLUMN_TEXT(strColumns, "SessionDuration"  , sep);
    WX_COLUMN_TEXT(strColumns, "PersonCount"      , sep);
    WX_COLUMN_TEXT(strColumns, "PersonList"       , sep);

    for (int i = 0; i<SESSION_PERSON_COUNT_MAX; i++)
    {
        strColumns += WriteTrailerCommon(i, sep);
        std::string index = (i<=9) ? "0" + std::to_string(i) : std::to_string(i);

        WX_COLUMN_TEXT(strColumns, "PersonC2STransPackets_" + index, sep);
        WX_COLUMN_TEXT(strColumns, "PersonS2CTransPackets_" + index, sep);
        WX_COLUMN_TEXT(strColumns, "PersonC2STransBytes_"   + index, sep);
        WX_COLUMN_TEXT(strColumns, "PersonS2CTransBytes_"   + index, sep);
        WX_COLUMN_TEXT(strColumns, "PersonStartTime_"       + index, sep);
        WX_COLUMN_TEXT(strColumns, "PersonLastActiveTime_"  + index, sep);
        WX_COLUMN_TEXT(strColumns, "PersonResv_"            + index, sep);
    }

    return strColumns;
}

template<>
std::string WxcsSessionBase<WxcsZoomPerson>::toStrRecordLine(char sep) const
{
    //find first last
    uint32_t time_first = 0;
    int time_ans   = 0;
    int time_last  = 0;
    int is_video   = 0;
    int has_ans    = 0;
    int count      = 0;

    for (auto iter = personMap_.begin(); iter != personMap_.end(); iter++)
    {
        count++;
        if(0 == time_first)
        {
            time_first = iter->second->first;
            time_ans   = iter->second->ans;
        }
        if(0 == time_last)
        {
            time_first = iter->second->last;
        }
        if(iter->second->first < time_first)
        {
            time_first = iter->second->first;
            time_ans   = iter->second->ans;
        }
        if(iter->second->last > time_last)
        {
            time_last = iter->second->last;
        }
        if((iter->second->C2S_V_Packet + iter->second->S2C_V_Packet) > 10)
        {
            is_video = 1;
        }
        if(iter->second->ans > 0)
        {
            has_ans = 1;
        }
    }

    int wait    = 0;
    int talk    = 0;
    if(has_ans)
    {
        wait = time_ans  - time_first;
        talk = time_last - time_ans;
    }
    else
    {
        wait = time_last - time_first;
        talk = 0;
    }

    std::string strLine;
    // 写入头部信息
    CONS_RECORD_FIELD_TEXT(strLine , "zoom"                                     , sep); // DevNO
    CONS_RECORD_FIELD_TEXT(strLine , "42"                                       , sep); // LineNO
    CONS_RECORD_FIELD_TIME(strLine , time_first                                 , sep); // SessionBegin
    CONS_RECORD_FIELD_TIME(strLine , time_last                                  , sep); // SessionEnd
    CONS_RECORD_FIELD_NUM (strLine , has_ans                                    , sep); // IsAnswered
    CONS_RECORD_FIELD_NUM (strLine , wait                                       , sep); // SessionWait
    CONS_RECORD_FIELD_NUM (strLine , talk                                       , sep); // SessionTalk
    CONS_RECORD_FIELD_NUM (strLine , is_video                                   , sep); // IsVideo
    CONS_RECORD_FIELD_NUM (strLine , count                                      , sep); // PersonCount
    CONS_RECORD_FIELD     (strLine , getPersonList()                            , sep); // PersonList

    ////写入每个人的数据
    int iChecked = 0;
    for (auto iter = personMap_.begin();
         iter != personMap_.end() && iChecked < SESSION_GROUP_PERSON_COUNT_MAX;
         iter++, iChecked++)
    {
        strLine += iter->second->toStrRecord(sep);
    }

    // 接着写入空白行
    for (; iChecked < SESSION_PERSON_COUNT_MAX; iChecked++)
    {
        strLine += WxcsZoomPerson::toStrBlankRecord(sep);
    }

    return strLine;
}


std::string WxcsSession<WxcsPositionPerson>::toStrRecordLine(char sep) const
{
    std::string strLine;

    // 写入头部信息
    CONS_RECORD_FIELD_TEXT(strLine, "wxp",                                   sep); // DevNO
    CONS_RECORD_FIELD_TEXT(strLine, "007",                                   sep); // LineNO
    CONS_RECORD_FIELD_TIME(strLine, sessionStartTime_,                       sep); // SessionStartTime
    CONS_RECORD_FIELD_NUM(strLine,  personMap_.size(),                       sep); // PersonCount
    CONS_RECORD_FIELD(strLine,      getPersonList(),                         sep); // PersonList
    CONS_RECORD_FIELD(strLine,      getLongitude(),                          sep); // Longitude
    CONS_RECORD_FIELD(strLine,      getLatitude(),                           sep); // Latitude
    CONS_RECORD_FIELD(strLine,      personMap_.begin()->second->getZoom(),   sep); // Zoom

    //写入每个人的数据
    int iChecked = 0;
    for (auto iter = personMap_.begin();
        iter != personMap_.end() && iChecked < SESSION_POSITION_PERSON_COUNT_MAX;
        iter++, iChecked++)
    {
        strLine += iter->second->toStrRecord(sep);
    }

    // 接着写入空白行
    for (; iChecked < SESSION_POSITION_PERSON_COUNT_MAX; iChecked++)
    {
        strLine += WxcsPositionPerson::toStrBlankRecord(sep);
    }

    return strLine;
}


template<>
std::string WxcsSessionBase<WxcsQQFilePerson>::getColumnList(char sep)
{
    #define QQ_FILE_MAX_MEMBER_COUNT 50
    std::string str;
    WX_COLUMN_TEXT(str, "wxcs",             sep);
    WX_COLUMN_TEXT(str, "LineNo",           sep);
    WX_COLUMN_TEXT(str, "CapDate",          sep);
    WX_COLUMN_TEXT(str, "isGroup",          sep);
    WX_COLUMN_TEXT(str, "GroupID",          sep);
    WX_COLUMN_TEXT(str, "PersonCount",      sep);
    WX_COLUMN_TEXT(str, "PersonList",       sep);
    WX_COLUMN_TEXT(str, "FileEncodeID",     sep);
    WX_COLUMN_TEXT(str, "FileName",         sep);
    WX_COLUMN_TEXT(str, "FileType",         sep);
    WX_COLUMN_TEXT(str, "FileSize",         sep);
    WX_COLUMN_TEXT(str, "isPicture",        sep);
    WX_COLUMN_TEXT(str, "picture_width",    sep);
    WX_COLUMN_TEXT(str, "picture_height",   sep);

    for (int i = 0; i < QQ_FILE_MAX_MEMBER_COUNT; i++)
    {
        str += WriteTrailerCommon(i, sep);
        std::string index = (i<=9) ? "0" + std::to_string(i) : std::to_string(i);

        WX_COLUMN_TEXT(str, "PersonQQNum_"              + index, sep);
        WX_COLUMN_TEXT(str, "PersonIsSender_"           + index, sep);
        WX_COLUMN_TEXT(str, "PersonHost_"               + index, sep);
        WX_COLUMN_TEXT(str, "PersonMethod_"             + index, sep);
        WX_COLUMN_TEXT(str, "PersonURL_"                + index, sep);
        WX_COLUMN_TEXT(str, "PersonAgent_"              + index, sep);
        WX_COLUMN_TEXT(str, "PersonAcceptEncoding_"     + index, sep);
        WX_COLUMN_TEXT(str, "PersonAcceptLanguage_"     + index, sep);
        WX_COLUMN_TEXT(str, "PersonConnection_"         + index, sep);
        WX_COLUMN_TEXT(str, "PersonContentLength_"      + index, sep);
        WX_COLUMN_TEXT(str, "PersonCookie_"             + index, sep);
        WX_COLUMN_TEXT(str, "PersonCacheControl_"       + index, sep);
        WX_COLUMN_TEXT(str, "PersonNetType_"            + index, sep);
        WX_COLUMN_TEXT(str, "PersonTimestamp_"          + index, sep);
        WX_COLUMN_TEXT(str, "PersonResv_"               + index, sep);
    }

    return str;
}

std::string WxcsSession<WxcsQQFilePerson>::toStrRecordLine(char sep) const
{
    std::string str;
    CONS_RECORD_FIELD_TEXT(str, "QQFile"                      , sep); // DevNO
    CONS_RECORD_FIELD_TEXT(str, "007"                         , sep); // LineNO
    CONS_RECORD_FIELD_TIME(str, time(NULL)                    , sep); // CapDate
    CONS_RECORD_FIELD_NUM (str, sessionType_                  , sep); // SessionType
    CONS_RECORD_FIELD_TEXT(str, ""                            , sep); // GroupID
    CONS_RECORD_FIELD_NUM (str, getPersonCount()              , sep); // PersonCount
    CONS_RECORD_FIELD     (str, getPersonList()               , sep); // PersonList
    CONS_RECORD_FIELD     (str, getSessionId()                , sep); // FileID
    CONS_RECORD_FIELD_TEXT(str, ""                            , sep); // FileName
    CONS_RECORD_FIELD_TEXT(str, ""                            , sep); // FileType
    CONS_RECORD_FIELD_TEXT(str, ""                            , sep); // FileSize
    CONS_RECORD_FIELD_NUM (str, get_ispictupe()               , sep); // isPicture
    CONS_RECORD_FIELD_TEXT(str, get_pictupe_w()               , sep); // picture_width
    CONS_RECORD_FIELD_TEXT(str, get_pictupe_h()               , sep); // picture_height

    int iChecked = 0;                           //人数计数器  累加
    for (auto it = personMap_.begin(); it != personMap_.end(); it++)
    {
        if(iChecked > QQ_FILE_MAX_MEMBER_COUNT)
        {
            break;
        }
        str += it->second->toStrRecord(sep);
        iChecked++;
    }

    for (; iChecked < QQ_FILE_MAX_MEMBER_COUNT; iChecked++)
    {
        str += WxcsQQFilePerson::toStrBlankRecord(sep);
    }

    return str;
}

///////////////////////WxcsSessionBase<WxcsQQGroupPerson>//////////////////////////////////////
/* template specialization for WxcsQQGroupPerson */
template<>
std::string WxcsSessionBase<WxcsQQGroupPerson>::getColumnList(char sep)
{
#define WX_COLUMN_TEXT(columns, text, sep)     columns += std::string(text) + sep

    std::string strColumns;

    WX_COLUMN_TEXT(strColumns, "DevNo", sep);
    WX_COLUMN_TEXT(strColumns, "LineNo", sep);
    WX_COLUMN_TEXT(strColumns, "CapDate", sep);
    WX_COLUMN_TEXT(strColumns, "SessionType", sep);
    WX_COLUMN_TEXT(strColumns, "SessionStartTime", sep);
    WX_COLUMN_TEXT(strColumns, "SessionStopTime", sep);
    WX_COLUMN_TEXT(strColumns, "SessionDuration", sep);
    WX_COLUMN_TEXT(strColumns, "PersonCount", sep);
    WX_COLUMN_TEXT(strColumns, "PersonList", sep);

    for (int i = 0; i < QQ_GROUP_MAX_MEMBER_COUNT; i++)
    {
        strColumns += WriteTrailerCommon(i, sep);
        std::string index = (i<=9) ? "0" + std::to_string(i) : std::to_string(i);

        WX_COLUMN_TEXT(strColumns, "PersonQQ_"              + index, sep);
        WX_COLUMN_TEXT(strColumns, "PersonC2STransPackets_" + index, sep);
        WX_COLUMN_TEXT(strColumns, "PersonS2CTransPackets_" + index, sep);
        WX_COLUMN_TEXT(strColumns, "PersonC2STransBytes_"   + index, sep);
        WX_COLUMN_TEXT(strColumns, "PersonS2CTransBytes_"   + index, sep);

        WX_COLUMN_TEXT(strColumns, "PersonStartTime_"       + index, sep);
        WX_COLUMN_TEXT(strColumns, "PersonLastActiveTime_"  + index, sep);
        WX_COLUMN_TEXT(strColumns, "PersonResv_"            + index, sep);
    }
    WX_COLUMN_TEXT(strColumns, "PersonListALL", sep);

    return strColumns;
}

template<>
std::string WxcsSessionBase<WxcsTencentMeetingPerson>::getColumnList(char sep)
{
#define WX_COLUMN_TEXT(columns, text, sep)     columns += std::string(text) + sep

    std::string strColumns;

    WX_COLUMN_TEXT(strColumns, "DevNo", sep);
    WX_COLUMN_TEXT(strColumns, "LineNo", sep);
    WX_COLUMN_TEXT(strColumns, "CapDate", sep);
    WX_COLUMN_TEXT(strColumns, "SessionType", sep);
    WX_COLUMN_TEXT(strColumns, "SessionStartTime", sep);
    WX_COLUMN_TEXT(strColumns, "SessionStopTime", sep);
    WX_COLUMN_TEXT(strColumns, "SessionDuration", sep);
    WX_COLUMN_TEXT(strColumns, "PersonCount", sep);
    WX_COLUMN_TEXT(strColumns, "SessionID", sep);
    WX_COLUMN_TEXT(strColumns, "PersonList", sep);

    for (int i = 0; i < SESSION_PERSON_COUNT_MAX; i++)
    {
        strColumns += WriteTrailerCommon(i, sep);
        std::string index = (i<=9) ? "0" + std::to_string(i) : std::to_string(i);

        WX_COLUMN_TEXT(strColumns, "PersonLoginID_"         + index, sep);
        WX_COLUMN_TEXT(strColumns, "PersonUID_"             + index, sep);

        WX_COLUMN_TEXT(strColumns, "PersonStartTime_"       + index, sep);
        WX_COLUMN_TEXT(strColumns, "PersonLastActiveTime_"  + index, sep);
        WX_COLUMN_TEXT(strColumns, "PersonResv_"            + index, sep);
    }

    return strColumns;
}

#ifdef DPI_WXA_WEB
template<>
std::string WxcsSessionBase<WxcsQQSinglePerson>::getColumnList(char sep)
{
#define WX_COLUMN_TEXT(columns, text, sep)     columns += std::string(text) + sep

    std::string strColumns;

    WX_COLUMN_TEXT(strColumns, "DevNo", sep);
    WX_COLUMN_TEXT(strColumns, "LineNo", sep);
    WX_COLUMN_TEXT(strColumns, "CapDate", sep);
    WX_COLUMN_TEXT(strColumns, "SessionType", sep);
    WX_COLUMN_TEXT(strColumns, "SessionStartTime", sep);
    WX_COLUMN_TEXT(strColumns, "SessionStopTime", sep);
    WX_COLUMN_TEXT(strColumns, "SessionDuration", sep);
    WX_COLUMN_TEXT(strColumns, "PersonCount", sep);
    WX_COLUMN_TEXT(strColumns, "PersonList", sep);

    for (int i = 0; i < QQ_GROUP_MAX_MEMBER_COUNT; i++)
    {
        strColumns += WriteTrailerCommon(i, sep);
        std::string index = (i<=9) ? "0" + std::to_string(i) : std::to_string(i);

        WX_COLUMN_TEXT(strColumns, "PersonQQ_"              + index, sep);
        WX_COLUMN_TEXT(strColumns, "PersonC2STransPackets_" + index, sep);
        WX_COLUMN_TEXT(strColumns, "PersonS2CTransPackets_" + index, sep);
        WX_COLUMN_TEXT(strColumns, "PersonC2STransBytes_"   + index, sep);
        WX_COLUMN_TEXT(strColumns, "PersonS2CTransBytes_"   + index, sep);

        WX_COLUMN_TEXT(strColumns, "PersonStartTime_"       + index, sep);
        WX_COLUMN_TEXT(strColumns, "PersonLastActiveTime_"  + index, sep);
        WX_COLUMN_TEXT(strColumns, "PersonResv_"            + index, sep);
    }
    WX_COLUMN_TEXT(strColumns, "PersonListALL", sep);

    return strColumns;
}
#endif
std::string WxcsSession<WxcsQQGroupPerson>::toStrRecordLine(char sep) const
{
    std::string strLine;

    static int allowMinCount = CFG->GetValueOf<int>("QQ_GROUP_MIN_PERSON_COUNT", 1);
    static int allowMaxCount = CFG->GetValueOf<int>("QQ_GROUP_MAX_PERSON_COUNT", QQ_GROUP_MAX_MEMBER_COUNT);

    int personCount =  personMap_.size() + extraQQSet_.size();
    if (personCount < allowMinCount || personCount > allowMaxCount)
        return strLine;

    // 音视频判断(语音聊天还是视频聊天，通过平均包大小判断)
    uint8_t sessionType = WXCS_SESSION_NONE;
    for (auto kv : personMap_)
    {
        if(kv.second->c2sPackCount_ && (kv.second->c2sByteCount_ / kv.second->c2sPackCount_ > VIDEO_PKT_SIZE))
        {
            sessionType = uint8_t(WXCS_SESSION_CHAT_GROUP_VIDEO);
        }
        else if(kv.second->s2cPackCount_ && (kv.second->s2cByteCount_ / kv.second->s2cPackCount_ > VIDEO_PKT_SIZE))
        {
            sessionType = uint8_t(WXCS_SESSION_CHAT_GROUP_VIDEO);
        }
        else {
            sessionType = uint8_t(WXCS_SESSION_CHAT_GROUP_AUDIO);
        }
   }

    // 计算通话时长
    uint32_t StartTime = sessionStartTime_;
    uint32_t LastTime  = sessionLastActiveTime_;

    CONS_RECORD_FIELD_TEXT(strLine , "wxcs"                                     , sep); // DevNO
    CONS_RECORD_FIELD_TEXT(strLine , "007"                                      , sep); // LineNO
    CONS_RECORD_FIELD_TIME(strLine , time(NULL)                                 , sep); // CapDate
    CONS_RECORD_FIELD_NUM(strLine  , sessionType                                , sep); // SessionType
    CONS_RECORD_FIELD_TIME(strLine , StartTime                                  , sep); // SessionStartTime
    CONS_RECORD_FIELD_TIME(strLine , LastTime                                   , sep); // SessionStopTime
    CONS_RECORD_FIELD_NUM(strLine  , LastTime - StartTime                       , sep); // SessionDuration
    CONS_RECORD_FIELD_NUM(strLine  , personCount                                , sep); // PersonCount
    CONS_RECORD_FIELD    (strLine  , getPersonListMaxMember()                            , sep); // PersonList

    int iChecked = 0;
    for (auto iter = personMap_.begin();
         iter != personMap_.end() && iChecked < QQ_GROUP_MAX_MEMBER_COUNT;
         iter++, iChecked++)
    {
        strLine += iter->second->toStrRecord(sep);
    }

    for (; iChecked < QQ_GROUP_MAX_MEMBER_COUNT; iChecked++)
    {
        strLine += WxcsQQGroupPerson::toStrBlankRecord(sep);
    }
    CONS_RECORD_FIELD    (strLine  , getPersonList()                            , sep); // PersonListALL
    return strLine;
}

std::string WxcsSession<WxcsQQGroupPerson>::getPersonList() const
{
    std::string strPersonList;

    for (auto kv : personMap_)
    {
        strPersonList += "(" + kv.second->getPrintablePersonID() + ',' + std::to_string(kv.second->selfQQ_) + ")" + ",";
    }

    for (auto qq : extraQQSet_)
    {
        strPersonList += "( ," + std::to_string(qq) + ")" + ",";
    }

    return strPersonList;
}

std::string WxcsSession<WxcsQQGroupPerson>::getPersonListMaxMember() const
{
    std::string strPersonList;
    int i = 0;
    for (auto kv : personMap_)
    {
        strPersonList += "(" + kv.second->getPrintablePersonID() + ',' + std::to_string(kv.second->selfQQ_) + ")" + ",";
        i++;
        if(i  == QQ_GROUP_MAX_MEMBER_COUNT + 1){
          return strPersonList;
        }
    }

    for (auto qq : extraQQSet_)
    {
        strPersonList += "( ," + std::to_string(qq) + ")" + ",";
        i++;
        if (i == QQ_GROUP_MAX_MEMBER_COUNT + 1) {
              return strPersonList;
        }
    }

    return strPersonList;
}

void WxcsSession<WxcsQQGroupPerson>::handleExtraQQList(uint64_t selfQQ, uint64_t* otherQQs, int len)
{
    static uint64_t allowMinQQ = (uint64_t)CFG->GetValueOf<long>("QQ_MIN_NUM", 10000);
    static uint64_t allowMaxQQ = (uint64_t)CFG->GetValueOf<long>("QQ_MAX_NUM", 100000000000);

    for (int i = 0; i < len; ++i)
    {
        uint64_t qq = otherQQs[i];
        if (qq == 0) // 后面没有数据了
            break;
        if (qq < allowMinQQ || qq > allowMaxQQ)
            continue;

        std::lock_guard<std::mutex> lck(personMtx_);
        auto it = std::find_if(personMap_.begin(), personMap_.end()
                            , [&](const std::pair<uint64_t, PersonPtr<WxcsQQGroupPerson> >& item) {
            return item.second->selfQQ_ == qq;
        });
        if (it != personMap_.end())
        {
            extraQQSet_.erase(qq);
        }
        else
        {
            extraQQSet_.insert(qq);
        }
    }

	// 确保已经确定的qq不在该容器中
	extraQQSet_.erase(selfQQ);
}

///////////////////////WxcsSessionBase<WxcsQQSinglePerson>//////////////////////////////////////
std::string WxcsSession<WxcsQQSinglePerson>::toStrRecordLine(char sep) const
{
    std::string strLine;

    // 音视频判断(语音聊天还是视频聊天，通过平均包大小判断)
    uint8_t sessionType = WXCS_SESSION_NONE;
    for (auto kv : personMap_)
    {
        if(kv.second->sessionType_!= 0){
            sessionType = kv.second->sessionType_;
        }else if(kv.second->c2sPackCount_ && (kv.second->c2sByteCount_ / kv.second->c2sPackCount_ > VIDEO_PKT_SIZE))
        {
            sessionType = uint8_t(WXCS_SESSION_CHAT_SINGLE_VIDEO);
        }
        else if(kv.second->s2cPackCount_ && (kv.second->s2cByteCount_ / kv.second->s2cPackCount_ > VIDEO_PKT_SIZE))
        {
            sessionType = uint8_t(WXCS_SESSION_CHAT_SINGLE_VIDEO);
        }
        else {
            sessionType = uint8_t(WXCS_SESSION_CHAT_SINGLE_AUDIO);
        }
   }

    // 计算通话时长
    uint32_t StartTime = sessionStartTime_;
    uint32_t LastTime  = sessionLastActiveTime_;
    uint8_t extraPerCount = (personMap_.size() > 1 || otherQQNum_ == 0 ) ? 0 : 1;

    CONS_RECORD_FIELD_TEXT(strLine , "wxcs"                                     , sep); // DevNO
    CONS_RECORD_FIELD_TEXT(strLine , "007"                                      , sep); // LineNO
    CONS_RECORD_FIELD_TIME(strLine , time(NULL)                                 , sep); // CapDate
    CONS_RECORD_FIELD_NUM(strLine  , sessionType                                , sep); // SessionType
    CONS_RECORD_FIELD_TIME(strLine , StartTime                                  , sep); // SessionStartTime
    CONS_RECORD_FIELD_TIME(strLine , LastTime                                   , sep); // SessionStopTime
    CONS_RECORD_FIELD_NUM(strLine  , LastTime - StartTime                       , sep); // SessionDuration
    CONS_RECORD_FIELD_NUM(strLine  , personMap_.size() + extraPerCount          , sep); // PersonCount
    if (this->only48_) {
      CONS_RECORD_FIELD(strLine      , get48PersonList()                            , sep); // PersonList
    }else {
      CONS_RECORD_FIELD(strLine      , getPersonList()                            , sep); // PersonList
    }

    int iChecked = 0;
    for (auto iter = personMap_.begin();
         iter != personMap_.end() && iChecked < QQ_GROUP_MAX_MEMBER_COUNT;
         iter++, iChecked++)
    {
        strLine += iter->second->toStrRecord(sep);
    }

    for (; iChecked < QQ_GROUP_MAX_MEMBER_COUNT; iChecked++)
    {
        strLine += WxcsQQSinglePerson::toStrBlankRecord(sep);
    }
    std::string empty;
    CONS_RECORD_FIELD(strLine      ,empty                            , sep); // PersonList

    return strLine;
}
std::string WxcsSession<WxcsQQSinglePerson>::get48PersonList() const{
    std::string strPersonList;
    std::set<uint64_t> num_list ;

    for (auto kv : personMap_)
    {
      num_list.insert(kv.second->qqSession_[0]) ;
      num_list.insert(kv.second->qqSession_[1]) ;
    }

    for (auto kv : num_list)
    {
      strPersonList += "( ," + std::to_string(kv) + ")" + ",";
    }
    return strPersonList;
}
std::string WxcsSession<WxcsQQSinglePerson>::getPersonList() const
{
    std::string strPersonList;

    for (auto kv : personMap_)
    {
        std::string print_ip = kv.second->getClientPubIP() == "" ? kv.second->getPrintablePersonID() : kv.second->getClientPubIP();
        strPersonList += "(" + print_ip + ',' + std::to_string(kv.second->selfQQ_) + ")" + ",";
    }

    std::string otherQQ = personMap_.size() > 1 ? "" : std::to_string(otherQQNum_);
    if (!otherQQ.empty())
    {
        char burrer_ip[64] = {0};
        inet_ntop(AF_INET, &QQPubIP_, burrer_ip, 64);
        std::string print_ip(burrer_ip);
        strPersonList += "(" + print_ip + "," + otherQQ + ")" + ",";
    }

    return strPersonList;
}

/* template specialization for WxcsSkypePerson */
template<>
std::string WxcsSessionBase<WxcsSkypePerson>::toStrRecordLine(char sep) const
{
    // 1 去重[map中的任意两个人的 MSISDN,IMSI,IMEI, (SrcIP+SrcPort), 可认为是一个人].
    // 2 计算建联的权重.
    // 3 将map集合 归整为 柱状图, 柱状图用矩阵存储

    //记录每个用户重复次数的结构体
    #define MAX_REPEAT_PERSON 10  // 去重后 人数 最大值
    #define MAX_PERSON_REPEAT 5  // 每个人被重复的次数
    struct{
        // 一个人的行为相关属性
        size_t MSISDN;                       // 记录 MSISDN
        size_t IMEI;                         // 记录 IMEI
        size_t IMSI;                         // 记录 IMSI
        size_t SrcIP;                        // 记录 用户的公网源IP
        size_t SrcPort;                      // 记录 用户的公网源Port
        size_t StackTop;                     // 记录 PersonID 出现的次数
        const  std::shared_ptr<WxcsSkypePerson>*StackSecond[MAX_PERSON_REPEAT];
        int    Quality[MAX_PERSON_REPEAT];   // 记录每个人的建联权重
        int    LAT    [MAX_PERSON_REPEAT];   // 记录每个人的 LastActiveTime
    } st_PersonRepeat[MAX_REPEAT_PERSON];    // 记录梳理后的结果, 类似柱状图,每个柱子代表一个人,高低代表一个人被重复的次数
    memset((&st_PersonRepeat), 0, sizeof(st_PersonRepeat));

    // 1.1 遍历数组 Person_MAP
    //找出每个人重复的次数 ([MSISDN][IMEI][IMSI][SRCIP+SRCPORT])
    for (auto it = personMap_.begin(); it != personMap_.end(); it++)
    {
        // 1.2 将 map 中的人与 st_PersonRepeat 逐一比对,收敛归纳
        for(int i=0; i < MAX_REPEAT_PERSON; i++)
        {
            // MSISDN, IMSI, IMEI, SRCIP+SRCPORT 任一两人的值相等,就认定为重复.
            if(     (it->second->getMsisdn() && it->second->getMsisdn() == st_PersonRepeat[i].MSISDN)
                ||  (it->second->getImsi()   && it->second->getImsi()   == st_PersonRepeat[i].IMSI)
                ||  (it->second->getImei()   && it->second->getImei()   == st_PersonRepeat[i].IMEI)
                ||  (it->second->getSrcIP()  == st_PersonRepeat[i].SrcIP&& it->second->getSrcPort() == st_PersonRepeat[i].SrcPort)
              )
            {
                // 成功识别 MAP中当前这个人在 已存在数组中
                // 将识别出为重复的人, 纳入为同一人的集合中
                int StackTop = st_PersonRepeat[i].StackTop;
                st_PersonRepeat[i].StackSecond[StackTop] = &it->second;

                // 记录最后活跃时间
                st_PersonRepeat[i].LAT        [StackTop] = it->second->getLastActiveTime();

                // 2.1 计算当前这个人的权重 权重具有优先级 (有的时候,建联设备不稳定, 会导致 3元组不全)
                st_PersonRepeat[i].Quality[StackTop] += (0 == it->second->getMsisdn() ) ? 0 : 1 << 3;
                st_PersonRepeat[i].Quality[StackTop] += (0 == it->second->getImsi()   ) ? 0 : 1 << 2;
                st_PersonRepeat[i].Quality[StackTop] += (0 == it->second->getImei()   ) ? 0 : 1 << 1;

                // st_PersonRepeat[i].StackSecond[0] 永远为最优人员
                // 2.2 根据权重, 调整最优结构的人在 index 为 0的位置中
                if(st_PersonRepeat[i].Quality[StackTop] > st_PersonRepeat[i].Quality[0])
                {
                    const  std::shared_ptr<WxcsSkypePerson>* swap = st_PersonRepeat[i].StackSecond[0];
                    st_PersonRepeat[i].StackSecond[0]             = st_PersonRepeat[i].StackSecond[StackTop];
                    st_PersonRepeat[i].StackSecond[StackTop]      = swap;

                    // 交换权重
                    int Quality                          = st_PersonRepeat[i].Quality[0];
                    st_PersonRepeat[i].Quality[0]        = st_PersonRepeat[i].Quality[StackTop];
                    st_PersonRepeat[i].Quality[StackTop] = Quality;
                }
                else
                // 如果两边建联的权重一样, 则选取最后活跃的这个人为去重后的 人选
                if(st_PersonRepeat[i].Quality[StackTop] == st_PersonRepeat[i].Quality[0] && st_PersonRepeat[i].LAT[StackTop] > st_PersonRepeat[i].LAT[0])
                {
                    const  std::shared_ptr<WxcsSkypePerson>* swap = st_PersonRepeat[i].StackSecond[0];
                    st_PersonRepeat[i].StackSecond[0]             = st_PersonRepeat[i].StackSecond[StackTop];
                    st_PersonRepeat[i].StackSecond[StackTop]      = swap;
                }


                //水涨船高, 柱状图上升一格
                if(StackTop < MAX_PERSON_REPEAT)
                {
                    st_PersonRepeat[i].StackTop++;
                }

                // 完善 共同属性(有的时候,建联设备不稳定, 会导致 3元组不全)
                st_PersonRepeat[i].MSISDN = (st_PersonRepeat[i].MSISDN == 0) ? it->second->getMsisdn()  : st_PersonRepeat[i].MSISDN;
                st_PersonRepeat[i].IMSI   = (st_PersonRepeat[i].IMSI   == 0) ? it->second->getImsi()    : st_PersonRepeat[i].IMSI;
                st_PersonRepeat[i].IMEI   = (st_PersonRepeat[i].IMEI   == 0) ? it->second->getImei()    : st_PersonRepeat[i].IMEI;
                st_PersonRepeat[i].SrcIP  = (st_PersonRepeat[i].SrcIP  == 0) ? it->second->getSrcIP()   : st_PersonRepeat[i].SrcIP;
                st_PersonRepeat[i].SrcPort= (st_PersonRepeat[i].SrcPort== 0) ? it->second->getSrcPort() : st_PersonRepeat[i].SrcPort;

                break;
            }
            else
            // 没有找到重复的, 添加新的三元组到 集合中
            // 必须是一个全新的, 干净的
            if(    0 == st_PersonRepeat[i].MSISDN
                && 0 == st_PersonRepeat[i].IMSI
                && 0 == st_PersonRepeat[i].IMEI
                && 0 == st_PersonRepeat[i].SrcIP
                && 0 == st_PersonRepeat[i].SrcPort
                && 0 == st_PersonRepeat[i].StackTop
              )
            {
                st_PersonRepeat[i].MSISDN  = it->second->getMsisdn();
                st_PersonRepeat[i].IMSI    = it->second->getImsi();
                st_PersonRepeat[i].IMEI    = it->second->getImei();
                st_PersonRepeat[i].SrcIP   = it->second->getSrcIP();
                st_PersonRepeat[i].SrcPort = it->second->getSrcPort();

                int StackTop = st_PersonRepeat[i].StackTop;
                st_PersonRepeat[i].StackSecond[StackTop] = &it->second;

                // 记录最后活跃时间
                st_PersonRepeat[i].LAT        [StackTop] = it->second->getLastActiveTime();

                // 2.1 计算当前这个人的权重(有的时候,建联设备不稳定, 会导致 3元组不全)
                st_PersonRepeat[i].Quality[StackTop] += (0 == it->second->getMsisdn() ) ? 0 : 1 << 3;
                st_PersonRepeat[i].Quality[StackTop] += (0 == it->second->getImsi()   ) ? 0 : 1 << 2;
                st_PersonRepeat[i].Quality[StackTop] += (0 == it->second->getImei()   ) ? 0 : 1 << 1;

                //水涨船高, 柱状图上升一格
                if(StackTop < MAX_PERSON_REPEAT)
                {
                    st_PersonRepeat[i].StackTop++;
                }
                break;
            }
        }
    } //end of map

    /********** 经过上面的处理, st_PersonRepeat中记录的数据已经去重了 ****/
    /********** 经过上面的处理, st_PersonRepeat中记录的数据已经去重了 ****/
    /********** 经过上面的处理, st_PersonRepeat中记录的数据已经去重了 ****/

    /* 当前版本由于时间紧急，所以暂不使用这些功能，但这些功能不代表不能实现

    */
    std::string strLine;
    // 1 寻找会话发起人, 会话接收人
    /*
    std::string strLine;        // TBL 记录
    std::string strCalling;     // 会话发起人, 已去重
    std::string strCalled;      // 会话接收人, 已去重
    if(0 == sessionType_)       // 0 为1对1通话
    {
        for(int i=0; i < MAX_REPEAT_PERSON; i++)
        {
            if(st_PersonRepeat[i].StackTop == 0)
            {
                continue;
            }
            const  std::shared_ptr<WxcsSkypePerson> Person = *st_PersonRepeat[i].StackSecond[0];
            if(CallTYpe_Calling == Person->Calling) // 会话发起标记
            {
                strCalling = Person->getPrintablePersonID();
            }
            else
            if(CallType_Called == Person->Calling) // 会话接收标记
            {
                strCalled = Person->getPrintablePersonID();
            }
        }
    }
    else
    if(1 == sessionType_)       // 1 为群通话
    {
        for(int i=0; i < MAX_REPEAT_PERSON; i++)
        {
            if(st_PersonRepeat[i].StackTop == 0)
            {
                continue;
            }
            const  std::shared_ptr<WxcsSkypePerson> Person = *st_PersonRepeat[i].StackSecond[0];

            // 群发起者才能呼叫等待时长, 被叫者无等待时长
            if(Person->RingTime > 0)
            {
                strCalling = Person->getPrintablePersonID();
            }
            else
            {
                const char*strPrefix = strCalled.empty() ? "" : ",";
                strCalled += strPrefix + Person->getPrintablePersonID();
            }
        }

    }
    */
    // 2 音视频判断, 判断包的大小
    // 上行或下行, 会话在中任意一人视频包个数大于 SESSION_PACKET_VIDEO_NUM, 认定为这是视频通话
    uint8_t sessionType   = 0;
    /*
    uint8_t sessionType   = 0;
    int     video_pkt_num = CFG->GetValueOf<int>("SESSION_PACKET_VIDEO_NUM", 10);
    for(int i=0; i < MAX_REPEAT_PERSON; i++)
    {
        if(st_PersonRepeat[i].StackTop == 0)
        {
            continue;
        }
        const  std::shared_ptr<WxcsSkypePerson> Person = *st_PersonRepeat[i].StackSecond[0];
        sessionType = Person->C2SVideoPackets > video_pkt_num ? 10 : 0;
        sessionType = Person->S2CVideoPackets > video_pkt_num ? 10 : 0;
    }
    */
    // 3 寻找最长的通话时长
    uint32_t StartTime     = 0;
    uint32_t LastTime      = 0;
    uint32_t DurationTime  = 0;
    for(int i=0; i < MAX_REPEAT_PERSON; i++)
    {
        if(st_PersonRepeat[i].StackTop == 0)
        {
            continue;
        }
        const  std::shared_ptr<WxcsSkypePerson> Person = *st_PersonRepeat[i].StackSecond[0];
        int Duration = Person->getLastActiveTime() - Person->StartTime;
        if( Duration >= DurationTime)
        {
            DurationTime = Duration;
            StartTime    = Person->StartTime;
            LastTime     = Person->getLastActiveTime();
        }
    }

    // 4 统计会话人员个数
    std::string PersonList;     // 会话人员  , 已去重
    int         PersonCount = 0;// 会话人数  , 已去重
    for(int i=0; i < MAX_REPEAT_PERSON; i++)
    {
        if(st_PersonRepeat[i].StackTop == 0)
        {
            continue;
        }
        const  std::shared_ptr<WxcsSkypePerson> Person = *st_PersonRepeat[i].StackSecond[0];
        PersonCount++;
        const char* spl = (PersonList.empty()) ? "" : ",";
        PersonList += (spl + Person->getPrintablePersonID());

    }

    // 写 TBL 头部内容
    CONS_RECORD_FIELD_TEXT(strLine , "wxcs"                                     , sep); // DevNO
    CONS_RECORD_FIELD_TEXT(strLine , "007"                                      , sep); // LineNO
    CONS_RECORD_FIELD_TIME(strLine , time(NULL)                                 , sep); // CapDate
    CONS_RECORD_FIELD_NUM(strLine  , sessionType+sessionType_                   , sep); // SessionType
    CONS_RECORD_FIELD_TIME(strLine , StartTime                                  , sep); // SessionStartTime
    CONS_RECORD_FIELD_TIME(strLine , LastTime                                   , sep); // SessionStopTime
    CONS_RECORD_FIELD_NUM(strLine  , DurationTime                               , sep); // SessionDuration
    CONS_RECORD_FIELD_NUM(strLine  , PersonCount                                , sep); // PersonCount
    CONS_RECORD_FIELD    (strLine  , PersonList                                 , sep); // PersonList

    // 写 TBL 每个人
    int iChecked = 0;    //人数计数器  累加
    for(int i=0; i < MAX_REPEAT_PERSON; i++)
    {
        if(st_PersonRepeat[i].StackTop >= 1)
        {
            strLine += (*st_PersonRepeat[i].StackSecond[0])->toStrRecord(sep);
            iChecked++;
        }
    }

    // 人数不够, TBL 补齐
    for (; iChecked < SESSION_PERSON_COUNT_MAX; iChecked++)
    {
        strLine += WxcsSkypePerson::toStrBlankRecord(sep);
    }

    return strLine;
}

/* template specialization for WxcsTencentMeetingPerson */


std::string WxcsSession<WxcsTencentMeetingPerson>::getPersonList() const{
    std::string strPersonList;

    for (auto kv : personMap_)
    {
        strPersonList +=
            "(" + kv.second->getPrintablePersonID() + "," + kv.second->selfUID + ")" + ",";
    }

    return strPersonList;
}

std::string WxcsSession<WxcsTencentMeetingPerson>::toStrRecordLine(char sep) const
{
    std::string strLine;


    uint32_t firstActiveTime = 0;
    uint32_t lastActiveTime = 0;

    for (auto& kv : personMap_)
    {
        if (firstActiveTime == 0 || kv.second->firstActiveTime < firstActiveTime)
            firstActiveTime = kv.second->firstActiveTime;
        if (kv.second->lastActiveTime > lastActiveTime)
            lastActiveTime = kv.second->lastActiveTime;
    }
    // 写入头部信息
    CONS_RECORD_FIELD_TEXT(strLine, "tencent_meeting",               sep); // DevNO
    CONS_RECORD_FIELD_TEXT(strLine, "001",                          sep); // LineNO
    CONS_RECORD_FIELD_TIME(strLine, time(NULL),                     sep); // CapDate
    CONS_RECORD_FIELD_NUM(strLine  , sessionType_                                , sep); // SessionType
    CONS_RECORD_FIELD_TIME(strLine , firstActiveTime                                  , sep); // SessionStartTime
    CONS_RECORD_FIELD_TIME(strLine , lastActiveTime                                   , sep); // SessionStopTime
    CONS_RECORD_FIELD_NUM(strLine  , lastActiveTime - firstActiveTime                       , sep); // SessionDuration
    CONS_RECORD_FIELD_NUM(strLine  , personMap_.size()                                , sep); // PersonCount
    CONS_RECORD_FIELD_TEXT(strLine  , getPrintableSessionId()                                , sep); // PersonCount

    CONS_RECORD_FIELD(strLine      , getPersonList()                            , sep); // PersonList
    // 写入每个人的信息
    int iChecked = 0;
    for (auto& kv : personMap_)
    {
        if (iChecked >= SESSION_PERSON_COUNT_MAX)
            break;

        strLine += kv.second->toStrRecord(sep);
        iChecked++;
    }

    // 人数不够, TBL 补齐
    for (; iChecked < SESSION_PERSON_COUNT_MAX; iChecked++)
    {
        strLine += WxcsTencentMeetingPerson::toStrBlankRecord(sep);
    }

    return strLine;
}
