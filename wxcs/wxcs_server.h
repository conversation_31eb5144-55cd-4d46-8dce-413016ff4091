/****************************************************************************************
 * 文 件 名 : wxcs_server.h
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : zhengsw      '2019.01.02
* 编    码 : zhengsw      '2019.01.02
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2019 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#ifndef _WXCS_SERVER_H_
#define _WXCS_SERVER_H_

#include <set>
#include <muduo/net/TcpServer.h>

#include "wxcs_types.h"
#include "wxcs_session_keeper.h"
#include "wxcs_tbl_writer.h"
#include "wxcs_content_writer.h"
#include "wxcs_voice.h"
#include "wxcs_loc_sharing.h"

/* forward declaration */
namespace muduo
{   namespace net
    {
        class InetAddress;
        class EventLoop;
        class EventLoopThread;
    }
};

/* forward declaration */
class WxcsProtoCodec;

/*
  WxcsServer
*/
class WxcsServer
{
public:
    WxcsServer(muduo::net::EventLoop *pLoop, uint16_t port, const std::string &strTblDir);

    ~WxcsServer();

    int start();

    void checkSessionTimeout();
    void checkWXASessionTimeout();
    void checkZOOMSessionTimeout();
    void checkWXGHSessionTimeout();
    void checkWXPSessionTimeout();
    void checkQQGSessionTimeout();
    void checkQQSSessionTimeout();
    void checkQQFileSessionTimeout();
    void checkSkypeChatSessionTimeout();
    void CheckLocSharingSessionTimeout();
    void checkTencentMeetingSessionTimeout();
    void checkBatChatSessionTimeout();
private:
   /*ADD_S by yangna 2020-09-16 */
    void checkWxcsTimeout();
    void checkTblWriteTimeout();
    void WxaTblWriteTimeout();
    void ZoomTblWriteTimeout();
    void GroupHeadTblWriteTimeout();
    void QQFileTblWriteTimeout();
    void PositionTblWriteTimeout();
    void QQVoipTblWriteTimeout();
    void SkypeTblWriteTimeout();
    void LocSharingTblWriteTimeout();
    void TencentMeetingTblWriteTimeout();
    void BatChatTblWriteTimeout();
    void tblWriteTimeout(wxcsTblWriter& tblWriter);
/*ADD_E by yangna 2020-09-16 */


public:
    static void handleSignal(int signal);

private:
    void onConnection(const muduo::net::TcpConnectionPtr& conn);

    void onSignal(int signal);
    void onMessage(const muduo::net::TcpConnectionPtr&,
                   const std::string& message,
                   muduo::Timestamp);

    void ProcessZoomChat(const unsigned char*pdata, int len);
    void ProcessMediaChat(const unsigned char*pdata, int len);
    void ProcessGroupHead(const unsigned char*pdata, int len);
    void ProcessPersonPosition(const unsigned char*pdata, int len);
    void ProcessQQGroupChat(const unsigned char*pdata, int len);
    void ProcessQQSingleChat(const unsigned char*pdata, int len);
    void ProcessQQFile(const unsigned char*pdata, int len);
    void ProcessWXGroupHeadData(const unsigned char*pdata, int len);
    void ProcessSkypeMediaChat(const unsigned char*pdata, int len);
    void ProcessWXRelation(const unsigned char *pdata, int len);
    // void ProcessWXPeer(const unsigned char *pdata, int len);
    void ProcessQQEvent(const unsigned char*pdata, int len);
    void ProcessTencentMeeting(const unsigned char*pdata, int len);
    void ProcessBatChat(const unsigned char*pdata, int len);

    int onRemveAudioSession(uint32_t time, SessionPtr<WxcsAudioPerson> & session);
    int onRemveZoomSession(uint32_t time, SessionPtr<WxcsZoomPerson> & session);
    int onRemveGroupHeadSession(uint32_t time, const SessionPtr<WxcsGroupHeadPerson> & session);
    int onRemvePersonPosition(uint32_t time, const SessionPtr<WxcsPositionPerson> & session);
    int onRemveQQGroupSession(uint32_t time, const SessionPtr<WxcsQQGroupPerson> & session);
    int onRemveQQSingleSession(uint32_t time, const SessionPtr<WxcsQQSinglePerson> & session);
    int onRemveQQFileSession(uint32_t time, const SessionPtr<WxcsQQFilePerson> & session);
    int onRemveSkypeChatSession(uint32_t time, SessionPtr<WxcsSkypePerson> & session);
    int onRemveLocSharingSession(uint32_t time, SessionPtr<WxcsLocSharingPerson> & session);
    int onRemveTencentMeetingSession(uint32_t time, SessionPtr<WxcsTencentMeetingPerson> & session);
    int onRemveBatChatSession(uint32_t time, SessionPtr<WxcsBatChatPerson> & session);

private:
    WxcsContentWriter contentWriter_;

private: // sessions and tbl writer for wxa
    // WxcsSessionKeeper<WxcsAudioPerson>     audioSessionKeeper_;
    // WxcsAudioSessionKeeper                  audioSessionKeeper_;
    wxcsTblWriter                           wxaTblWriter_;

private: // sessions and tbl writer for zoom
    WxcsSessionKeeper<WxcsZoomPerson>      zoomSessionKeeper_;
    wxcsTblWriter                          zoomTblWriter_;

private: // sessions and tbl writer for wxgh
    WxcsSessionKeeper<WxcsGroupHeadPerson> groupHeadSessionKeeper_;
    wxcsTblWriter                          wxghTblWriter_;

private: // sessions and tbl writer for QQFile
    WxcsSessionKeeper<WxcsQQFilePerson>    QQFileSessionKeeper_;
    wxcsTblWriter                          QQFileTblWriter_;

private: // sessions and tbl writer for wxp
    WxcsSessionKeeper<WxcsPositionPerson>  positionSessionKeeper_;
    wxcsTblWriter                          wxpTblWriter_;

private: // sessions and tbl writer for qq chat, use only one tbl writer for two session type
    WxcsSessionKeeper<WxcsQQGroupPerson>   qqGroupSessionKeeper_;
    WxcsQQSingleSessionKeeper              qqSingleSessionKeeper_;
    wxcsTblWriter                          qqavTblWriter_;

private: // sessions and tbl writer for skype
    WxcsSessionKeeper<WxcsSkypePerson>     skypeSessionKeeper_;
    wxcsTblWriter                          skypeTblWriter_;

private:
    wxcsTblWriter                           locSharingTblWrite_;

private: // sessions and tbl writer for tencent meeting
    WxcsSessionKeeper<WxcsTencentMeetingPerson> tencentMeetingSessionKeeper_;
    wxcsTblWriter                           tencentMeetingTblWriter_;

private: // sessions and tbl writer for bat chat
    WxcsSessionKeeper<WxcsBatChatPerson>    batChatSessionKeeper_;
    wxcsTblWriter                           batChatTblWriter_;

public:/*QQ活动事件处理 */
    WxcsQQEventPerson                       qqEventMap_;

private: // interesting person list
    std::set<uint64_t>                     interestingMsisdnSet_;
    std::set<uint64_t>                     interestingImsiSet_;
    std::set<uint64_t>                     interestingImeiSet_;

private: // network
    muduo::net::InetAddress                 addr_;
    muduo::net::TcpServer                   server_;
    WxcsProtoCodec                          *pProtoCodec_;
    std::set<muduo::net::TcpConnectionPtr>  connSet_;
    std::mutex                              connMutex_;

private: // timer
    EventLoopThread                         *timerTh_;
    EventLoop                               *timerLoop_; // not owned

private:
    static std::set<WxcsServer *>           cs_serverSet;

private:
    WXRelation                              wx_ios_rela_;
    // WXPeers                                 wx_peers_;
};

/*
  WxcsProtoCodec
*/
class WxcsProtoCodec
{
public:
  typedef std::function<void (const muduo::net::TcpConnectionPtr&,
                                const std::string& message,
                                muduo::Timestamp)> StringMessageCallback;
public:
  explicit WxcsProtoCodec(const StringMessageCallback& cb = NULL);

  void setMessageCallback(const WxcsProtoCodec::StringMessageCallback &cb);

  void onMessage(const muduo::net::TcpConnectionPtr& conn,
                 muduo::net::Buffer* buf,
                 muduo::Timestamp receiveTime);

private:
  StringMessageCallback messageCallback_;
  const static size_t kHeaderLen = sizeof (ST_wxcsProtoHdr);
};

#endif /* _WXCS_SERVER_H_ */
