/****************************************************************************************
 * 文 件 名 : wxcs_person.h
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : root      '2019-01-14
* 编    码 : root      '2019-01-14
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#ifndef _WXCS_PERSON_H_
#define _WXCS_PERSON_H_

#include "wxcs_def.h"

#include <string>
#include <memory>
#include <functional>
#include <unordered_map>
#include <vector>
#include <map>


/* wxcs中每个用户信息 */
typedef struct wxrela_per_info_
{
    uint32_t timestamp;      //用户活跃
    char wxid[24];
    char uin[24];
} WxrelaPerInfo;


class WxcsPerson
{
public:
    WxcsPerson();
    WxcsPerson(uint8_t *SessionID,     int SessionIDLen,      uint8_t SessionType,
               uint16_t client_port, uint16_t server_port,
               uint32_t client_ip,   uint32_t server_ip,
               uint32_t PersonLastActiveTime,
               ST_trailer *trailer
            );

public:
    uint64_t    getMsisdn() const;
    uint64_t    getImsi() const;
    uint64_t    getImei() const;
    uint64_t    getSrcIP() const;
    uint64_t    getSrcPort() const;
    uint32_t    getLastActiveTime() const;
    uint32_t    getDPI_NodeID() const;
    bool        wasUncrediableId() const;

    std::string getPrintablePersonID() const;
    std::string getPrintablePersonMobileID() const;
    std::string getPrintableSessionID() const;
    std::string getPrintableULI() const;
    std::string getStrTrailer(char sep) const;
    std::string getClientIP() const;
    std::string getServerIP() const;
    std::string getClientPubIP() const;

public:
    virtual uint64_t    getPersonID() const;
    virtual uint64_t    getPersonUUID() const;
    virtual std::string getDevName() const;
    virtual std::string getOperator() const;
    virtual std::string toStrRecord(char sep) const;
    static  std::string toStrBlankRecord(char sep);


private:
    bool      bHasLteInfo;

protected:
    std::string SessionID;
    uint8_t     SessionType;
    uint16_t    client_port;
    uint16_t    server_port;
    uint8_t     ip_version;
    uint32_t    cli_pub_ip;
    uint32_t    client_ip;
    uint32_t    server_ip;
    uint8_t     client_ipv6[16];
    uint8_t     server_ipv6[16];
    uint32_t    LastActiveTime;
    uint64_t    MSG_TimeStamp;
    uint8_t     trailerType;
    uint32_t    TEID;
    uint32_t    OUTTER_SRC;
    uint32_t    OUTTER_DST;
    uint64_t    MSISDN;
    uint64_t    IMEI;
    uint64_t    IMSI;
    uint32_t    TAC;
    std::string DevName;
    std::string Operator;
    std::string Area;

    uint8_t     HW_BFLAG;
    std::string HW_APN;
    uint8_t     HW_NCODE;
    uint32_t    HW_ECGI;
    uint16_t    HW_LAC;
    uint16_t    HW_SAC;
    uint16_t    HW_CI;

    uint16_t    RT_PLMN_ID;
    uint32_t    RT_ULI;
    uint8_t     RT_BS;               // 低4bit有效，0xb 标示 2G，0xc 标示 3G，0xD标示4G
    std::string RT_DomainName;
    uint32_t    DPI_Node_ID;         // 来自哪个DPI节点
    uint64_t    nci;

    uint8_t     jc_rat;             // jl 网络类型标签，和 RT_BS 互斥

    std::string fixed_account_;

};

template <typename PersonType>
using PersonPtr = std::shared_ptr<PersonType>;

/* Wxcs Group Head */
class WxcsGroupHeadPerson : public WxcsPerson
{
public:
    WxcsGroupHeadPerson();
    WxcsGroupHeadPerson(ST_wxGroupHead* pGroupHeadUpdate);

public:
    virtual int         dumpGroupHeadPerson() const;
    virtual std::string toStrRecord(char sep) const;
    static  std::string toStrBlankRecord(char sep);

private:
    std::string PersonHost;
    std::string PersonUrl;
    std::string PersonUin;
    std::string PersonRef;
    std::string PersonAgent;
    std::string PersonAcceptEncoding;
    std::string PersonAcceptLanguage;

};

/* Wxcs Position */
class WxcsPositionPerson : public WxcsPerson
{
public:
    WxcsPositionPerson();
    WxcsPositionPerson(ST_wxPosition* pPositionUpdate);
    std::string getLongitude() const;
    std::string getLatitude()  const;
    std::string getUrl()       const;
    std::string getZoom()      const;

public:
    virtual int         dumpPositionPerson() const;
    virtual std::string toStrRecord(char sep) const;
    static  std::string toStrBlankRecord(char sep);

private:
    std::string PersonHost;
    std::string PersonUrl;
    std::string PersonAgent;
    std::string PersonAcceptEncoding;

    std::string longitude;
    std::string latitude;
    std::string zoom;
};


/* WxcsZoomPerson */
class WxcsZoomPerson : public WxcsPerson
{
public:
    WxcsZoomPerson();
    WxcsZoomPerson(ST_ZOOM_person *p);

    std::string toStrRecord(char sep) const;
    static  std::string toStrBlankRecord(char sep);

public:
    uint32_t  C2S_A_Packet;            // 用户传输到服务器报文总数
    uint32_t  S2C_A_Packet;            // 服务器传输到用户报文总数
    uint32_t  C2S_V_Packet;            // 用户传输到服务器视频报文数量
    uint32_t  S2C_V_Packet;            // 服务器传输到用户视频报文数量
    uint32_t  C2S_C_Packet;            // 用户传输到服务器控制报文数量
    uint32_t  S2C_C_Packet;            // 服务器传输到用户控制报文数量
    uint32_t  C2S_K_Packet;            // 用户传输到服务器控制报文数量
    uint32_t  S2C_K_Packet;            // 服务器传输到用户控制报文数量
    uint32_t  isTimeout;               // 是否已经超时
    uint32_t  first;                   // 用户首次出现的时间
    uint32_t  ans;                     // 时间
    uint32_t  last;                    // 时间
};

/*! WxcsQQGroupPerson */
class WxcsQQGroupPerson : public WxcsPerson
{
public:
    WxcsQQGroupPerson();
    explicit WxcsQQGroupPerson(ST_qqGroup* msg);

public:
    virtual std::string toStrRecord(char sep) const;
    static std::string toStrBlankRecord(char sep);

public:
    uint64_t selfQQ_ = 0;
    uint32_t startTime_ = 0;

    uint32_t c2sPackCount_ = 0;
    uint32_t c2sByteCount_ = 0;
    uint32_t s2cPackCount_ = 0;
    uint32_t s2cByteCount_ = 0;
    bool isCurrent_ = false;
};

/*! WxcsQQSinglePerson */
class WxcsQQSinglePerson : public WxcsPerson
{
public:
    WxcsQQSinglePerson();
    explicit WxcsQQSinglePerson(ST_qqSingle* msg);

public:
    virtual std::string toStrRecord(char sep) const;
    static std::string toStrBlankRecord(char sep);

public:
    void setSessionId(const std::string& sessionId);
    void setPublicIP(uint32_t);
public:
    uint64_t selfQQ_ = 0;
    uint32_t startTime_ = 0;
    uint64_t qqSession_[2]  = {0};
    uint32_t c2sPackCount_ = 0;
    uint32_t c2sByteCount_ = 0;
    uint32_t s2cPackCount_ = 0;
    uint32_t s2cByteCount_ = 0;
    bool isTimeout_ = false;
    int sessionType_ = WXCS_SESSION_NONE;
};

/* WxcsQQFilePerson */
class WxcsQQFilePerson : public WxcsPerson
{
public:
    WxcsQQFilePerson();
    WxcsQQFilePerson(ST_QQ_File_MSG*p);

public:
    virtual std::string toStrRecord(char sep);
    static  std::string toStrBlankRecord(char sep);
    int get_ispicture(void)
    {
        return ispicture;
    }
    std::string get_picture_w(void)
    {
        return picture_width;
    }
    std::string get_picture_h(void)
    {
        return picture_height;
    }
    void setQQNum(size_t QQNum)
    {
        this->QQNum = QQNum;
    }

    void setQQNumFrom(std::string QQNumFrom)
    {
        this->QQNumFrom = QQNumFrom;
    }

    size_t getQQNum(void)
    {
        return QQNum;
    }

    int get_tblPushFlg(void)
    {
        return tblPushFlg;
    }

private:
    int             tblPushFlg    ; //是否推送TBL数据，0，默认值推送， 1：不推送， 不推送时将保留字段值填空
    char            isGroup       ; // 群行为?
    char            isSender      ; // 1:文件发送者, 0:文件为接受者

    char            ispicture     ; // 是不是图片
    size_t          QQNum ;         // QQ号码
    std::string     picture_width ; // 图片的长
    std::string     picture_height; // 图片的高

    std::string     Filecode      ; // 被编码后的文件名
    std::string     Method        ;
    std::string     Host          ;
    std::string     URI           ;
    std::string     URL           ;
    std::string     User_Agent    ;
    std::string     AcceptEncoding;
    std::string     AcceptLanguage;
    std::string     Connection    ;
    std::string     ContentLength ;
    std::string     Cookie        ;
    std::string     CacheControl  ;
    std::string     NetType       ;
    std::string     QQNumFrom     ; //QQ号码来源

};

/* WxcsSkypePerson */
class WxcsSkypePerson : public WxcsPerson
{
public:
    WxcsSkypePerson();
    WxcsSkypePerson(ST_SkypeMediaSessionAlive *pAliveMsg);

    uint32_t            getC2STransPackets() const;
    uint32_t            getS2CTransPackets() const;
    virtual std::string toStrRecord(char sep) const;
    static  std::string toStrBlankRecord(char sep);

public:
    uint32_t  StartTime;               // 用户首次出现的时间
    uint32_t  C2STransPackets;         // 用户传输到服务器报文总数
    uint32_t  S2CTransPackets;         // 服务器传输到用户报文总数
    uint32_t  C2STransBytes;           // 用户传输到服务器字节总数
    uint32_t  S2CTransBytes;           // 服务器传输到用户字节总数
    uint32_t  isTimeout;               // 是否已经超时
};

/* WxcsTencentMeetingPerson */
class WxcsTencentMeetingPerson : public WxcsPerson
{
public:
    WxcsTencentMeetingPerson();
    WxcsTencentMeetingPerson(ST_TencentMeeting *pMeetingMsg);

    virtual std::string toStrRecord(char sep) const;
    static  std::string toStrBlankRecord(char sep);

public:
    uint64_t  sessionId;               // 会议SessionID
    std::string selfLoginNum;          // 个人常规账号
    std::string selfUID;          // 个人常规账号
    uint32_t  firstActiveTime;         // 首次活跃时间
    uint32_t  lastActiveTime;          // 最后活跃时间
    uint8_t   isTimeout;               // 是否已经超时
};

/* WxcsBatChatPerson */
class WxcsBatChatPerson : public WxcsPerson
{
public:
    WxcsBatChatPerson();
    WxcsBatChatPerson(ST_BatChat *pBatChatMsg);

    virtual std::string toStrRecord(char sep) const;
    static  std::string toStrBlankRecord(char sep);

public:
    uint32_t  sessionId[2];            // 会话SessionID
    uint32_t  c2sPackCount;            // 客户端到服务器包数
    uint32_t  c2sByteCount;            // 客户端到服务器字节数
    uint32_t  s2cPackCount;            // 服务器到客户端包数
    uint32_t  s2cByteCount;            // 服务器到客户端字节数
    uint32_t  firstActiveTime;         // 首次活跃时间
    uint32_t  lastActiveTime;          // 最后活跃时间
    uint8_t   isTimeout;               // 是否已经超时
};

/**/
class WXRelation
{
    using UserListOfIP = std::unordered_map<std::string, std::vector<WxrelaPerInfo>>;
public:
    WXRelation() = default;
    WXRelation(const WXRelation &) = delete;
    WXRelation & operator=(const WXRelation &) = delete;
    ~WXRelation() = default;

public:
    inline UserListOfIP &GetUserList() { return this->userListOfIP; }

public:
    // bool orderInsert(uint32_t _clientIP, WxrelaPerInfo &_relaPerInfo);  //DEPRECATED
    bool orderInsert(std::string & _clientIP, WxrelaPerInfo & _relaPerInfo);
    // std::shared_ptr<WxrelaPerInfo> findNearestPer(uint32_t _clientIp, uint32_t _timeStamp);//DEPRECATED
    std::shared_ptr<WxrelaPerInfo> findNearestPer(std::string & _clientIp, uint32_t _timeStamp);

private:
    UserListOfIP userListOfIP;
};


/*ADD_S by yangna 2020-08-20 */

/*QQ活动事件信息结构体 */
typedef struct
{
    unsigned int weight;    //权重
    unsigned int lastTime;  //更新时间
    size_t  IMEI;           // 国际移动设备标识
    size_t  IMSI;           // 国际移动用户识别码
}QQEventInfo;


class WxcsQQEventPerson
{
public:
    WxcsQQEventPerson();

    /*向MAP中插入数据 */
    int inserQQEventData(ST_QQEventAlive *pQQEvent);
    /*循环MAP中的数据，打印调试用 */
    void loopQQEventData();
    /*根据手机号码返回权重最大或者最小的QQ号码 */
    int getQQByMsisdnWithWeight(size_t msisdn, size_t &QQNum, unsigned int &weight, unsigned int &lastTime);
    /*根据手机号码返回权重最大或者最小的QQ号码 */
    int getMsisdnByQQWithWeight(size_t QQNum, size_t &msisdn, unsigned int &weight, unsigned int &lastTime);
    /*根据手机号码获取对应的所有QQ号码 */
    int getQQListByMsisdn(size_t msisdn,  std::map<size_t, QQEventInfo> &mapQQEventValue);
    /*获取所有的手机号码和QQ对应关系 */
    int getAllMsisdnQQ(std::map<size_t, std::map<size_t, QQEventInfo>> **mapQQEvent);

private:
    //std::map<手机号码, std::map<QQ号码, std::pair<权重, 更新时间>>>
    //std::map<size_t, std::map<size_t, std::pair<unsigned int, unsigned int>>>  MapQQEvent;
    //std::map<手机号码, std::map<QQ号码, QQ活动事件信息>>
    std::map<size_t, std::map<size_t, QQEventInfo>>  MapQQEvent;
    pthread_rwlock_t                                 qqEventlock; //定义和初始化读写锁
};

/*ADD_E by yangna 2020-08-20 */

#define CONS_RECORD_FIELD(rec, field, sep)          rec += "\"" + field + "\"" + sep
#define CONS_RECORD_FIELD_BYTES(rec, field, sep)    CONS_RECORD_FIELD(rec, ((field==0)?"":bytes_to_hexstring(field)), sep)
#define CONS_RECORD_FIELD_IP(rec, field, sep)       CONS_RECORD_FIELD(rec, ((field==0)?"":ip_addr_to_str(field)), sep)
#define CONS_RECORD_FIELD_NUM(rec, field, sep)      CONS_RECORD_FIELD(rec, std::to_string(field), sep)
#define CONS_RECORD_FIELD_TIME(rec, field, sep)     CONS_RECORD_FIELD(rec, unix_time_to_str(field), sep)
#define CONS_RECORD_FIELD_TEXT(rec, field, sep)     CONS_RECORD_FIELD(rec, std::string(field), sep)

#endif /* _WXCS_PERSON_H_ */
